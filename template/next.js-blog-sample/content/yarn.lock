# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@braintree/sanitize-url@npm:^6.0.1":
  version: 6.0.4
  resolution: "@braintree/sanitize-url@npm:6.0.4"
  checksum: 10c0/5d7bac57f3e49931db83f65aaa4fd22f96caa323bf0c7fcf6851fdbed179a8cf29eaa5dd372d340fc51ca5f44345ea5bc0196b36c8b16179888a7c9044313420
  languageName: node
  linkType: hard

"@emnapi/runtime@npm:^1.4.4":
  version: 1.4.5
  resolution: "@emnapi/runtime@npm:1.4.5"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10c0/37a0278be5ac81e918efe36f1449875cbafba947039c53c65a1f8fc238001b866446fc66041513b286baaff5d6f9bec667f5164b3ca481373a8d9cb65bfc984b
  languageName: node
  linkType: hard

"@headlessui/react@npm:^1.7.17":
  version: 1.7.19
  resolution: "@headlessui/react@npm:1.7.19"
  dependencies:
    "@tanstack/react-virtual": "npm:^3.0.0-beta.60"
    client-only: "npm:^0.0.1"
  peerDependencies:
    react: ^16 || ^17 || ^18
    react-dom: ^16 || ^17 || ^18
  checksum: 10c0/c0ece0db6ca15092439177a5322de50b60fa5fd90354ae0f999b3e56abab0065ed54fa7b4b69994ec1bdc23adc6ae9919d7dd57f97922d0b9bb6515d27e3a7e5
  languageName: node
  linkType: hard

"@img/sharp-darwin-arm64@npm:0.34.3":
  version: 0.34.3
  resolution: "@img/sharp-darwin-arm64@npm:0.34.3"
  dependencies:
    "@img/sharp-libvips-darwin-arm64": "npm:1.2.0"
  dependenciesMeta:
    "@img/sharp-libvips-darwin-arm64":
      optional: true
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-darwin-x64@npm:0.34.3":
  version: 0.34.3
  resolution: "@img/sharp-darwin-x64@npm:0.34.3"
  dependencies:
    "@img/sharp-libvips-darwin-x64": "npm:1.2.0"
  dependenciesMeta:
    "@img/sharp-libvips-darwin-x64":
      optional: true
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-arm64@npm:1.2.0":
  version: 1.2.0
  resolution: "@img/sharp-libvips-darwin-arm64@npm:1.2.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-x64@npm:1.2.0":
  version: 1.2.0
  resolution: "@img/sharp-libvips-darwin-x64@npm:1.2.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm64@npm:1.2.0":
  version: 1.2.0
  resolution: "@img/sharp-libvips-linux-arm64@npm:1.2.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm@npm:1.2.0":
  version: 1.2.0
  resolution: "@img/sharp-libvips-linux-arm@npm:1.2.0"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-ppc64@npm:1.2.0":
  version: 1.2.0
  resolution: "@img/sharp-libvips-linux-ppc64@npm:1.2.0"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-s390x@npm:1.2.0":
  version: 1.2.0
  resolution: "@img/sharp-libvips-linux-s390x@npm:1.2.0"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-x64@npm:1.2.0":
  version: 1.2.0
  resolution: "@img/sharp-libvips-linux-x64@npm:1.2.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-arm64@npm:1.2.0":
  version: 1.2.0
  resolution: "@img/sharp-libvips-linuxmusl-arm64@npm:1.2.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-x64@npm:1.2.0":
  version: 1.2.0
  resolution: "@img/sharp-libvips-linuxmusl-x64@npm:1.2.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-linux-arm64@npm:0.34.3":
  version: 0.34.3
  resolution: "@img/sharp-linux-arm64@npm:0.34.3"
  dependencies:
    "@img/sharp-libvips-linux-arm64": "npm:1.2.0"
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm64":
      optional: true
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-arm@npm:0.34.3":
  version: 0.34.3
  resolution: "@img/sharp-linux-arm@npm:0.34.3"
  dependencies:
    "@img/sharp-libvips-linux-arm": "npm:1.2.0"
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm":
      optional: true
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-ppc64@npm:0.34.3":
  version: 0.34.3
  resolution: "@img/sharp-linux-ppc64@npm:0.34.3"
  dependencies:
    "@img/sharp-libvips-linux-ppc64": "npm:1.2.0"
  dependenciesMeta:
    "@img/sharp-libvips-linux-ppc64":
      optional: true
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-s390x@npm:0.34.3":
  version: 0.34.3
  resolution: "@img/sharp-linux-s390x@npm:0.34.3"
  dependencies:
    "@img/sharp-libvips-linux-s390x": "npm:1.2.0"
  dependenciesMeta:
    "@img/sharp-libvips-linux-s390x":
      optional: true
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-x64@npm:0.34.3":
  version: 0.34.3
  resolution: "@img/sharp-linux-x64@npm:0.34.3"
  dependencies:
    "@img/sharp-libvips-linux-x64": "npm:1.2.0"
  dependenciesMeta:
    "@img/sharp-libvips-linux-x64":
      optional: true
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-arm64@npm:0.34.3":
  version: 0.34.3
  resolution: "@img/sharp-linuxmusl-arm64@npm:0.34.3"
  dependencies:
    "@img/sharp-libvips-linuxmusl-arm64": "npm:1.2.0"
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-x64@npm:0.34.3":
  version: 0.34.3
  resolution: "@img/sharp-linuxmusl-x64@npm:0.34.3"
  dependencies:
    "@img/sharp-libvips-linuxmusl-x64": "npm:1.2.0"
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-wasm32@npm:0.34.3":
  version: 0.34.3
  resolution: "@img/sharp-wasm32@npm:0.34.3"
  dependencies:
    "@emnapi/runtime": "npm:^1.4.4"
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@img/sharp-win32-arm64@npm:0.34.3":
  version: 0.34.3
  resolution: "@img/sharp-win32-arm64@npm:0.34.3"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-win32-ia32@npm:0.34.3":
  version: 0.34.3
  resolution: "@img/sharp-win32-ia32@npm:0.34.3"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@img/sharp-win32-x64@npm:0.34.3":
  version: 0.34.3
  resolution: "@img/sharp-win32-x64@npm:0.34.3"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@mdx-js/mdx@npm:^2.2.1, @mdx-js/mdx@npm:^2.3.0":
  version: 2.3.0
  resolution: "@mdx-js/mdx@npm:2.3.0"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/mdx": "npm:^2.0.0"
    estree-util-build-jsx: "npm:^2.0.0"
    estree-util-is-identifier-name: "npm:^2.0.0"
    estree-util-to-js: "npm:^1.1.0"
    estree-walker: "npm:^3.0.0"
    hast-util-to-estree: "npm:^2.0.0"
    markdown-extensions: "npm:^1.0.0"
    periscopic: "npm:^3.0.0"
    remark-mdx: "npm:^2.0.0"
    remark-parse: "npm:^10.0.0"
    remark-rehype: "npm:^10.0.0"
    unified: "npm:^10.0.0"
    unist-util-position-from-estree: "npm:^1.0.0"
    unist-util-stringify-position: "npm:^3.0.0"
    unist-util-visit: "npm:^4.0.0"
    vfile: "npm:^5.0.0"
  checksum: 10c0/719384d8e72abd3e83aa2fd3010394636e32cc0e5e286b6414427ef03121397586ce97ec816afcc4d2b22ba65939c3801a8198e04cf921dd597c0aa9fd75dbb4
  languageName: node
  linkType: hard

"@mdx-js/react@npm:^2.2.1, @mdx-js/react@npm:^2.3.0":
  version: 2.3.0
  resolution: "@mdx-js/react@npm:2.3.0"
  dependencies:
    "@types/mdx": "npm:^2.0.0"
    "@types/react": "npm:>=16"
  peerDependencies:
    react: ">=16"
  checksum: 10c0/6d647115703dbe258f7fe372499fa8c6fe17a053ff0f2a208111c9973a71ae738a0ed376770445d39194d217e00e1a015644b24f32c2f7cb4f57988de0649b15
  languageName: node
  linkType: hard

"@napi-rs/simple-git-android-arm-eabi@npm:0.1.19":
  version: 0.1.19
  resolution: "@napi-rs/simple-git-android-arm-eabi@npm:0.1.19"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@napi-rs/simple-git-android-arm64@npm:0.1.19":
  version: 0.1.19
  resolution: "@napi-rs/simple-git-android-arm64@npm:0.1.19"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@napi-rs/simple-git-darwin-arm64@npm:0.1.19":
  version: 0.1.19
  resolution: "@napi-rs/simple-git-darwin-arm64@npm:0.1.19"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@napi-rs/simple-git-darwin-x64@npm:0.1.19":
  version: 0.1.19
  resolution: "@napi-rs/simple-git-darwin-x64@npm:0.1.19"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@napi-rs/simple-git-freebsd-x64@npm:0.1.19":
  version: 0.1.19
  resolution: "@napi-rs/simple-git-freebsd-x64@npm:0.1.19"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@napi-rs/simple-git-linux-arm-gnueabihf@npm:0.1.19":
  version: 0.1.19
  resolution: "@napi-rs/simple-git-linux-arm-gnueabihf@npm:0.1.19"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@napi-rs/simple-git-linux-arm64-gnu@npm:0.1.19":
  version: 0.1.19
  resolution: "@napi-rs/simple-git-linux-arm64-gnu@npm:0.1.19"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@napi-rs/simple-git-linux-arm64-musl@npm:0.1.19":
  version: 0.1.19
  resolution: "@napi-rs/simple-git-linux-arm64-musl@npm:0.1.19"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@napi-rs/simple-git-linux-powerpc64le-gnu@npm:0.1.19":
  version: 0.1.19
  resolution: "@napi-rs/simple-git-linux-powerpc64le-gnu@npm:0.1.19"
  conditions: os=linux & cpu=powerpc64le & libc=glibc
  languageName: node
  linkType: hard

"@napi-rs/simple-git-linux-s390x-gnu@npm:0.1.19":
  version: 0.1.19
  resolution: "@napi-rs/simple-git-linux-s390x-gnu@npm:0.1.19"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@napi-rs/simple-git-linux-x64-gnu@npm:0.1.19":
  version: 0.1.19
  resolution: "@napi-rs/simple-git-linux-x64-gnu@npm:0.1.19"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@napi-rs/simple-git-linux-x64-musl@npm:0.1.19":
  version: 0.1.19
  resolution: "@napi-rs/simple-git-linux-x64-musl@npm:0.1.19"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@napi-rs/simple-git-win32-arm64-msvc@npm:0.1.19":
  version: 0.1.19
  resolution: "@napi-rs/simple-git-win32-arm64-msvc@npm:0.1.19"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@napi-rs/simple-git-win32-x64-msvc@npm:0.1.19":
  version: 0.1.19
  resolution: "@napi-rs/simple-git-win32-x64-msvc@npm:0.1.19"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@napi-rs/simple-git@npm:^0.1.9":
  version: 0.1.19
  resolution: "@napi-rs/simple-git@npm:0.1.19"
  dependencies:
    "@napi-rs/simple-git-android-arm-eabi": "npm:0.1.19"
    "@napi-rs/simple-git-android-arm64": "npm:0.1.19"
    "@napi-rs/simple-git-darwin-arm64": "npm:0.1.19"
    "@napi-rs/simple-git-darwin-x64": "npm:0.1.19"
    "@napi-rs/simple-git-freebsd-x64": "npm:0.1.19"
    "@napi-rs/simple-git-linux-arm-gnueabihf": "npm:0.1.19"
    "@napi-rs/simple-git-linux-arm64-gnu": "npm:0.1.19"
    "@napi-rs/simple-git-linux-arm64-musl": "npm:0.1.19"
    "@napi-rs/simple-git-linux-powerpc64le-gnu": "npm:0.1.19"
    "@napi-rs/simple-git-linux-s390x-gnu": "npm:0.1.19"
    "@napi-rs/simple-git-linux-x64-gnu": "npm:0.1.19"
    "@napi-rs/simple-git-linux-x64-musl": "npm:0.1.19"
    "@napi-rs/simple-git-win32-arm64-msvc": "npm:0.1.19"
    "@napi-rs/simple-git-win32-x64-msvc": "npm:0.1.19"
  dependenciesMeta:
    "@napi-rs/simple-git-android-arm-eabi":
      optional: true
    "@napi-rs/simple-git-android-arm64":
      optional: true
    "@napi-rs/simple-git-darwin-arm64":
      optional: true
    "@napi-rs/simple-git-darwin-x64":
      optional: true
    "@napi-rs/simple-git-freebsd-x64":
      optional: true
    "@napi-rs/simple-git-linux-arm-gnueabihf":
      optional: true
    "@napi-rs/simple-git-linux-arm64-gnu":
      optional: true
    "@napi-rs/simple-git-linux-arm64-musl":
      optional: true
    "@napi-rs/simple-git-linux-powerpc64le-gnu":
      optional: true
    "@napi-rs/simple-git-linux-s390x-gnu":
      optional: true
    "@napi-rs/simple-git-linux-x64-gnu":
      optional: true
    "@napi-rs/simple-git-linux-x64-musl":
      optional: true
    "@napi-rs/simple-git-win32-arm64-msvc":
      optional: true
    "@napi-rs/simple-git-win32-x64-msvc":
      optional: true
  checksum: 10c0/b8088c2a21c4f8ce5bee69933c23efba16738eb07b58a44e6c1eac042770f1b6f6687bfc3cb98e8ef5f2dbd29754e37678743fdf5a0e0c3ffcfaef165a86a895
  languageName: node
  linkType: hard

"@next/env@npm:15.4.2":
  version: 15.4.2
  resolution: "@next/env@npm:15.4.2"
  checksum: 10c0/8ee793035f0a7fb55335ea2bc2794ccd68747e6ab8c591001662dbf1ba328f43b04d02bb3a6c9d6a60ed18b55d44cc157b724121ce8dbea8584ca41272eb90a4
  languageName: node
  linkType: hard

"@next/swc-darwin-arm64@npm:15.4.2":
  version: 15.4.2
  resolution: "@next/swc-darwin-arm64@npm:15.4.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-darwin-x64@npm:15.4.2":
  version: 15.4.2
  resolution: "@next/swc-darwin-x64@npm:15.4.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-gnu@npm:15.4.2":
  version: 15.4.2
  resolution: "@next/swc-linux-arm64-gnu@npm:15.4.2"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-musl@npm:15.4.2":
  version: 15.4.2
  resolution: "@next/swc-linux-arm64-musl@npm:15.4.2"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-linux-x64-gnu@npm:15.4.2":
  version: 15.4.2
  resolution: "@next/swc-linux-x64-gnu@npm:15.4.2"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-x64-musl@npm:15.4.2":
  version: 15.4.2
  resolution: "@next/swc-linux-x64-musl@npm:15.4.2"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-win32-arm64-msvc@npm:15.4.2":
  version: 15.4.2
  resolution: "@next/swc-win32-arm64-msvc@npm:15.4.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-win32-x64-msvc@npm:15.4.2":
  version: 15.4.2
  resolution: "@next/swc-win32-x64-msvc@npm:15.4.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@swc/helpers@npm:0.5.15":
  version: 0.5.15
  resolution: "@swc/helpers@npm:0.5.15"
  dependencies:
    tslib: "npm:^2.8.0"
  checksum: 10c0/33002f74f6f885f04c132960835fdfc474186983ea567606db62e86acd0680ca82f34647e8e610f4e1e422d1c16fce729dde22cd3b797ab1fd9061a825dabca4
  languageName: node
  linkType: hard

"@tanstack/react-virtual@npm:^3.0.0-beta.60":
  version: 3.13.12
  resolution: "@tanstack/react-virtual@npm:3.13.12"
  dependencies:
    "@tanstack/virtual-core": "npm:3.13.12"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/0eda3d5691ec3bf93a1cdaa955f4972c7aa9a5026179622824bb52ff8c47e59ee4634208e52d77f43ffb3ce435ee39a0899d6a81f6316918ce89d68122490371
  languageName: node
  linkType: hard

"@tanstack/virtual-core@npm:3.13.12":
  version: 3.13.12
  resolution: "@tanstack/virtual-core@npm:3.13.12"
  checksum: 10c0/483f38761b73db05c181c10181f0781c1051be3350ae5c378e65057e5f1fdd6606e06e17dbaad8a5e36c04b208ea1a1344cacd4eca0dcde60f335cf398e4d698
  languageName: node
  linkType: hard

"@theguild/remark-mermaid@npm:^0.0.5":
  version: 0.0.5
  resolution: "@theguild/remark-mermaid@npm:0.0.5"
  dependencies:
    mermaid: "npm:^10.2.2"
    unist-util-visit: "npm:^5.0.0"
  peerDependencies:
    react: ^18.2.0
  checksum: 10c0/3471a32a87d50f7eb699f15ff181f9a3698209951ef0fab1e928ea391275105286b0391e46cca4dd22d30dcab934e5c7eb6573c341f5d8543ca5bcb2f60cc916
  languageName: node
  linkType: hard

"@theguild/remark-npm2yarn@npm:^0.2.0":
  version: 0.2.1
  resolution: "@theguild/remark-npm2yarn@npm:0.2.1"
  dependencies:
    npm-to-yarn: "npm:^2.1.0"
    unist-util-visit: "npm:^5.0.0"
  checksum: 10c0/69380ac3814bcf2f9c00c8e375d97e55220adea04d9c887df1b6ac888b726a8a7aaf391ed80ceca1756cfa39d572221d12f681bc1a5f3fdf49a0ed59f7c3addc
  languageName: node
  linkType: hard

"@types/acorn@npm:^4.0.0":
  version: 4.0.6
  resolution: "@types/acorn@npm:4.0.6"
  dependencies:
    "@types/estree": "npm:*"
  checksum: 10c0/5a65a1d7e91fc95703f0a717897be60fa7ccd34b17f5462056274a246e6690259fe0a1baabc86fd3260354f87245cb3dc483346d7faad2b78fc199763978ede9
  languageName: node
  linkType: hard

"@types/d3-scale-chromatic@npm:^3.0.0":
  version: 3.1.0
  resolution: "@types/d3-scale-chromatic@npm:3.1.0"
  checksum: 10c0/93c564e02d2e97a048e18fe8054e4a935335da6ab75a56c3df197beaa87e69122eef0dfbeb7794d4a444a00e52e3123514ee27cec084bd21f6425b7037828cc2
  languageName: node
  linkType: hard

"@types/d3-scale@npm:^4.0.3":
  version: 4.0.9
  resolution: "@types/d3-scale@npm:4.0.9"
  dependencies:
    "@types/d3-time": "npm:*"
  checksum: 10c0/4ac44233c05cd50b65b33ecb35d99fdf07566bcdbc55bc1306b2f27d1c5134d8c560d356f2c8e76b096e9125ffb8d26d95f78d56e210d1c542cb255bdf31d6c8
  languageName: node
  linkType: hard

"@types/d3-time@npm:*":
  version: 3.0.4
  resolution: "@types/d3-time@npm:3.0.4"
  checksum: 10c0/6d9e2255d63f7a313a543113920c612e957d70da4fb0890931da6c2459010291b8b1f95e149a538500c1c99e7e6c89ffcce5554dd29a31ff134a38ea94b6d174
  languageName: node
  linkType: hard

"@types/debug@npm:^4.0.0":
  version: 4.1.12
  resolution: "@types/debug@npm:4.1.12"
  dependencies:
    "@types/ms": "npm:*"
  checksum: 10c0/5dcd465edbb5a7f226e9a5efd1f399c6172407ef5840686b73e3608ce135eeca54ae8037dcd9f16bdb2768ac74925b820a8b9ecc588a58ca09eca6acabe33e2f
  languageName: node
  linkType: hard

"@types/estree-jsx@npm:^1.0.0":
  version: 1.0.5
  resolution: "@types/estree-jsx@npm:1.0.5"
  dependencies:
    "@types/estree": "npm:*"
  checksum: 10c0/07b354331516428b27a3ab99ee397547d47eb223c34053b48f84872fafb841770834b90cc1a0068398e7c7ccb15ec51ab00ec64b31dc5e3dbefd624638a35c6d
  languageName: node
  linkType: hard

"@types/estree@npm:*, @types/estree@npm:^1.0.0, @types/estree@npm:^1.0.6":
  version: 1.0.8
  resolution: "@types/estree@npm:1.0.8"
  checksum: 10c0/39d34d1afaa338ab9763f37ad6066e3f349444f9052b9676a7cc0252ef9485a41c6d81c9c4e0d26e9077993354edf25efc853f3224dd4b447175ef62bdcc86a5
  languageName: node
  linkType: hard

"@types/hast@npm:^2.0.0":
  version: 2.3.10
  resolution: "@types/hast@npm:2.3.10"
  dependencies:
    "@types/unist": "npm:^2"
  checksum: 10c0/16daac35d032e656defe1f103f9c09c341a6dc553c7ec17b388274076fa26e904a71ea5ea41fd368a6d5f1e9e53be275c80af7942b9c466d8511d261c9529c7e
  languageName: node
  linkType: hard

"@types/hast@npm:^3.0.0":
  version: 3.0.4
  resolution: "@types/hast@npm:3.0.4"
  dependencies:
    "@types/unist": "npm:*"
  checksum: 10c0/3249781a511b38f1d330fd1e3344eed3c4e7ea8eff82e835d35da78e637480d36fad37a78be5a7aed8465d237ad0446abc1150859d0fde395354ea634decf9f7
  languageName: node
  linkType: hard

"@types/js-yaml@npm:^4.0.0":
  version: 4.0.9
  resolution: "@types/js-yaml@npm:4.0.9"
  checksum: 10c0/24de857aa8d61526bbfbbaa383aa538283ad17363fcd5bb5148e2c7f604547db36646440e739d78241ed008702a8920665d1add5618687b6743858fae00da211
  languageName: node
  linkType: hard

"@types/katex@npm:^0.16.0":
  version: 0.16.7
  resolution: "@types/katex@npm:0.16.7"
  checksum: 10c0/68dcb9f68a90513ec78ca0196a142e15c2a2c270b1520d752bafd47a99207115085a64087b50140359017d7e9c870b3c68e7e4d36668c9e348a9ef0c48919b5a
  languageName: node
  linkType: hard

"@types/mdast@npm:^3.0.0":
  version: 3.0.15
  resolution: "@types/mdast@npm:3.0.15"
  dependencies:
    "@types/unist": "npm:^2"
  checksum: 10c0/fcbf716c03d1ed5465deca60862e9691414f9c43597c288c7d2aefbe274552e1bbd7aeee91b88a02597e88a28c139c57863d0126fcf8416a95fdc681d054ee3d
  languageName: node
  linkType: hard

"@types/mdast@npm:^4.0.0":
  version: 4.0.4
  resolution: "@types/mdast@npm:4.0.4"
  dependencies:
    "@types/unist": "npm:*"
  checksum: 10c0/84f403dbe582ee508fd9c7643ac781ad8597fcbfc9ccb8d4715a2c92e4545e5772cbd0dbdf18eda65789386d81b009967fdef01b24faf6640f817287f54d9c82
  languageName: node
  linkType: hard

"@types/mdx@npm:^2.0.0":
  version: 2.0.13
  resolution: "@types/mdx@npm:2.0.13"
  checksum: 10c0/5edf1099505ac568da55f9ae8a93e7e314e8cbc13d3445d0be61b75941226b005e1390d9b95caecf5dcb00c9d1bab2f1f60f6ff9876dc091a48b547495007720
  languageName: node
  linkType: hard

"@types/ms@npm:*":
  version: 2.1.0
  resolution: "@types/ms@npm:2.1.0"
  checksum: 10c0/5ce692ffe1549e1b827d99ef8ff71187457e0eb44adbae38fdf7b9a74bae8d20642ee963c14516db1d35fa2652e65f47680fdf679dcbde52bbfadd021f497225
  languageName: node
  linkType: hard

"@types/node@npm:^18.0.0":
  version: 18.19.120
  resolution: "@types/node@npm:18.19.120"
  dependencies:
    undici-types: "npm:~5.26.4"
  checksum: 10c0/fc436b93a3c9367b69d11a1c5d70cc923df6ea60141dd508ce2a248160c3bf8a04c658548a59df8ab9148b68bd58954fb603917baab3e2bc9da567f655534e5a
  languageName: node
  linkType: hard

"@types/prop-types@npm:*":
  version: 15.7.15
  resolution: "@types/prop-types@npm:15.7.15"
  checksum: 10c0/b59aad1ad19bf1733cf524fd4e618196c6c7690f48ee70a327eb450a42aab8e8a063fbe59ca0a5701aebe2d92d582292c0fb845ea57474f6a15f6994b0e260b2
  languageName: node
  linkType: hard

"@types/react-dom@npm:^18.0.5":
  version: 18.3.7
  resolution: "@types/react-dom@npm:18.3.7"
  peerDependencies:
    "@types/react": ^18.0.0
  checksum: 10c0/8bd309e2c3d1604a28a736a24f96cbadf6c05d5288cfef8883b74f4054c961b6b3a5e997fd5686e492be903c8f3380dba5ec017eff3906b1256529cd2d39603e
  languageName: node
  linkType: hard

"@types/react@npm:>=16":
  version: 19.1.8
  resolution: "@types/react@npm:19.1.8"
  dependencies:
    csstype: "npm:^3.0.2"
  checksum: 10c0/4908772be6dc941df276931efeb0e781777fa76e4d5d12ff9f75eb2dcc2db3065e0100efde16fde562c5bafa310cc8f50c1ee40a22640459e066e72cd342143e
  languageName: node
  linkType: hard

"@types/react@npm:^18.0.14":
  version: 18.3.23
  resolution: "@types/react@npm:18.3.23"
  dependencies:
    "@types/prop-types": "npm:*"
    csstype: "npm:^3.0.2"
  checksum: 10c0/49331800b76572eb2992a5c44801dbf8c612a5f99c8f4e4200f06c7de6f3a6e9455c661784a6c5469df96fa45622cb4a9d0982c44e6a0d5719be5f2ef1f545ed
  languageName: node
  linkType: hard

"@types/unist@npm:*, @types/unist@npm:^3.0.0":
  version: 3.0.3
  resolution: "@types/unist@npm:3.0.3"
  checksum: 10c0/2b1e4adcab78388e088fcc3c0ae8700f76619dbcb4741d7d201f87e2cb346bfc29a89003cfea2d76c996e1061452e14fcd737e8b25aacf949c1f2d6b2bc3dd60
  languageName: node
  linkType: hard

"@types/unist@npm:^2, @types/unist@npm:^2.0.0":
  version: 2.0.11
  resolution: "@types/unist@npm:2.0.11"
  checksum: 10c0/24dcdf25a168f453bb70298145eb043cfdbb82472db0bc0b56d6d51cd2e484b9ed8271d4ac93000a80da568f2402e9339723db262d0869e2bf13bc58e081768d
  languageName: node
  linkType: hard

"@ungap/structured-clone@npm:^1.0.0":
  version: 1.3.0
  resolution: "@ungap/structured-clone@npm:1.3.0"
  checksum: 10c0/0fc3097c2540ada1fc340ee56d58d96b5b536a2a0dab6e3ec17d4bfc8c4c86db345f61a375a8185f9da96f01c69678f836a2b57eeaa9e4b8eeafd26428e57b0a
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.0.0":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10c0/4c54868fbef3b8d58927d5e33f0a4de35f59012fe7b12cf9dfbb345fb8f46607709e1c4431be869a23fb63c151033d84c4198fa9f79385cec34fcb1dd53974c1
  languageName: node
  linkType: hard

"acorn@npm:^8.0.0":
  version: 8.15.0
  resolution: "acorn@npm:8.15.0"
  bin:
    acorn: bin/acorn
  checksum: 10c0/dec73ff59b7d6628a01eebaece7f2bdb8bb62b9b5926dcad0f8931f2b8b79c2be21f6c68ac095592adb5adb15831a3635d9343e6a91d028bbe85d564875ec3ec
  languageName: node
  linkType: hard

"ansi-sequence-parser@npm:^1.1.0":
  version: 1.1.3
  resolution: "ansi-sequence-parser@npm:1.1.3"
  checksum: 10c0/49649f14765b7864158f070747889d68048f1629024eae1ce82f548616fdd89c3717ba0fa7b39a766c58c7806307f78add99e41e3ccf5db8af4fb6f0f50b9f8a
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.1.0":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: "npm:^1.9.0"
  checksum: 10c0/ece5a8ef069fcc5298f67e3f4771a663129abd174ea2dfa87923a2be2abf6cd367ef72ac87942da00ce85bd1d651d4cd8595aebdb1b385889b89b205860e977b
  languageName: node
  linkType: hard

"arch@npm:^2.1.0":
  version: 2.2.0
  resolution: "arch@npm:2.2.0"
  checksum: 10c0/4ceaf8d8207817c216ebc4469742052cb0a097bc45d9b7fcd60b7507220da545a28562ab5bdd4dfe87921bb56371a0805da4e10d704e01f93a15f83240f1284c
  languageName: node
  linkType: hard

"arg@npm:1.0.0":
  version: 1.0.0
  resolution: "arg@npm:1.0.0"
  checksum: 10c0/10bbbda299b1a5d5f1cc6492bdea9413f148c36b58e7abc49e8b8337047eec5db154c1d2f99e942c4b777ae28215fc28506d303d7e30bcd80ca1ad7baeb6ce28
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: "npm:~1.0.2"
  checksum: 10c0/b2972c5c23c63df66bca144dbc65d180efa74f25f8fd9b7d9a0a6c88ae839db32df3d54770dcb6460cf840d232b60695d1a6b1053f599d84e73f7437087712de
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10c0/c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"astring@npm:^1.8.0":
  version: 1.9.0
  resolution: "astring@npm:1.9.0"
  bin:
    astring: bin/astring
  checksum: 10c0/e7519544d9824494e80ef0e722bb3a0c543a31440d59691c13aeaceb75b14502af536b23f08db50aa6c632dafaade54caa25f0788aa7550b6b2d6e2df89e0830
  languageName: node
  linkType: hard

"bail@npm:^2.0.0":
  version: 2.0.2
  resolution: "bail@npm:2.0.2"
  checksum: 10c0/25cbea309ef6a1f56214187004e8f34014eb015713ea01fa5b9b7e9e776ca88d0fdffd64143ac42dc91966c915a4b7b683411b56e14929fad16153fc026ffb8b
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001579":
  version: 1.0.30001727
  resolution: "caniuse-lite@npm:1.0.30001727"
  checksum: 10c0/f0a441c05d8925d728c2d02ce23b001935f52183a3bf669556f302568fe258d1657940c7ac0b998f92bc41383e185b390279a7d779e6d96a2b47881f56400221
  languageName: node
  linkType: hard

"ccount@npm:^2.0.0":
  version: 2.0.1
  resolution: "ccount@npm:2.0.1"
  checksum: 10c0/3939b1664390174484322bc3f45b798462e6c07ee6384cb3d645e0aa2f318502d174845198c1561930e1d431087f74cf1fe291ae9a4722821a9f4ba67e574350
  languageName: node
  linkType: hard

"chalk@npm:2.3.0":
  version: 2.3.0
  resolution: "chalk@npm:2.3.0"
  dependencies:
    ansi-styles: "npm:^3.1.0"
    escape-string-regexp: "npm:^1.0.5"
    supports-color: "npm:^4.0.0"
  checksum: 10c0/ff3d14e7b31b1acdcd06b0c3b8d00e08748d76a0f2a6cc86baa1fe2456ebd4dd45037315a58df7f3c1886153c5d0a35da8183d2757f7fad28eaef6dedd33b437
  languageName: node
  linkType: hard

"character-entities-html4@npm:^2.0.0":
  version: 2.1.0
  resolution: "character-entities-html4@npm:2.1.0"
  checksum: 10c0/fe61b553f083400c20c0b0fd65095df30a0b445d960f3bbf271536ae6c3ba676f39cb7af0b4bf2755812f08ab9b88f2feed68f9aebb73bb153f7a115fe5c6e40
  languageName: node
  linkType: hard

"character-entities-legacy@npm:^3.0.0":
  version: 3.0.0
  resolution: "character-entities-legacy@npm:3.0.0"
  checksum: 10c0/ec4b430af873661aa754a896a2b55af089b4e938d3d010fad5219299a6b6d32ab175142699ee250640678cd64bdecd6db3c9af0b8759ab7b155d970d84c4c7d1
  languageName: node
  linkType: hard

"character-entities@npm:^2.0.0":
  version: 2.0.2
  resolution: "character-entities@npm:2.0.2"
  checksum: 10c0/b0c645a45bcc90ff24f0e0140f4875a8436b8ef13b6bcd31ec02cfb2ca502b680362aa95386f7815bdc04b6464d48cf191210b3840d7c04241a149ede591a308
  languageName: node
  linkType: hard

"character-reference-invalid@npm:^2.0.0":
  version: 2.0.1
  resolution: "character-reference-invalid@npm:2.0.1"
  checksum: 10c0/2ae0dec770cd8659d7e8b0ce24392d83b4c2f0eb4a3395c955dce5528edd4cc030a794cfa06600fcdd700b3f2de2f9b8e40e309c0011c4180e3be64a0b42e6a1
  languageName: node
  linkType: hard

"client-only@npm:0.0.1, client-only@npm:^0.0.1":
  version: 0.0.1
  resolution: "client-only@npm:0.0.1"
  checksum: 10c0/9d6cfd0c19e1c96a434605added99dff48482152af791ec4172fb912a71cff9027ff174efd8cdb2160cc7f377543e0537ffc462d4f279bc4701de3f2a3c4b358
  languageName: node
  linkType: hard

"clipboardy@npm:1.2.2":
  version: 1.2.2
  resolution: "clipboardy@npm:1.2.2"
  dependencies:
    arch: "npm:^2.1.0"
    execa: "npm:^0.8.0"
  checksum: 10c0/c343ee1ff74fd7202b8e549575e0e09d36d122cd06b078b171cf9ee37f03479d53547a5792ee879145841122c11ee4419078ffec07daf3eda4fa800758c8f1d9
  languageName: node
  linkType: hard

"clsx@npm:^2.0.0":
  version: 2.1.1
  resolution: "clsx@npm:2.1.1"
  checksum: 10c0/c4c8eb865f8c82baab07e71bfa8897c73454881c4f99d6bc81585aecd7c441746c1399d08363dc096c550cceaf97bd4ce1e8854e1771e9998d9f94c4fe075839
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: "npm:1.1.3"
  checksum: 10c0/5ad3c534949a8c68fca8fbc6f09068f435f0ad290ab8b2f76841b9e6af7e0bb57b98cb05b0e19fe33f5d91e5a8611ad457e5f69e0a484caad1f7487fd0e8253c
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 10c0/566a3d42cca25b9b3cd5528cd7754b8e89c0eb646b7f214e8e2eaddb69994ac5f0557d9c175eb5d8f0ad73531140d9c47525085ee752a91a2ab15ab459caf6d6
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"color-string@npm:^1.9.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: "npm:^1.0.0"
    simple-swizzle: "npm:^0.2.2"
  checksum: 10c0/b0bfd74c03b1f837f543898b512f5ea353f71630ccdd0d66f83028d1f0924a7d4272deb278b9aef376cacf1289b522ac3fb175e99895283645a2dc3a33af2404
  languageName: node
  linkType: hard

"color@npm:^4.2.3":
  version: 4.2.3
  resolution: "color@npm:4.2.3"
  dependencies:
    color-convert: "npm:^2.0.1"
    color-string: "npm:^1.9.0"
  checksum: 10c0/7fbe7cfb811054c808349de19fb380252e5e34e61d7d168ec3353e9e9aacb1802674bddc657682e4e9730c2786592a4de6f8283e7e0d3870b829bb0b7b2f6118
  languageName: node
  linkType: hard

"comma-separated-tokens@npm:^2.0.0":
  version: 2.0.3
  resolution: "comma-separated-tokens@npm:2.0.3"
  checksum: 10c0/91f90f1aae320f1755d6957ef0b864fe4f54737f3313bd95e0802686ee2ca38bff1dd381964d00ae5db42912dd1f4ae5c2709644e82706ffc6f6842a813cdd67
  languageName: node
  linkType: hard

"commander@npm:7":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 10c0/8d690ff13b0356df7e0ebbe6c59b4712f754f4b724d4f473d3cc5b3fdcf978e3a5dc3078717858a2ceb50b0f84d0660a7f22a96cdc50fb877d0c9bb31593d23a
  languageName: node
  linkType: hard

"commander@npm:^8.3.0":
  version: 8.3.0
  resolution: "commander@npm:8.3.0"
  checksum: 10c0/8b043bb8322ea1c39664a1598a95e0495bfe4ca2fad0d84a92d7d1d8d213e2a155b441d2470c8e08de7c4a28cf2bc6e169211c49e1b21d9f7edc6ae4d9356060
  languageName: node
  linkType: hard

"cose-base@npm:^1.0.0":
  version: 1.0.3
  resolution: "cose-base@npm:1.0.3"
  dependencies:
    layout-base: "npm:^1.0.0"
  checksum: 10c0/a6e400b1d101393d6af0967c1353355777c1106c40417c5acaef6ca8bdda41e2fc9398f466d6c85be30290943ad631f2590569f67b3fd5368a0d8318946bd24f
  languageName: node
  linkType: hard

"cross-spawn@npm:^5.0.1":
  version: 5.1.0
  resolution: "cross-spawn@npm:5.1.0"
  dependencies:
    lru-cache: "npm:^4.0.1"
    shebang-command: "npm:^1.2.0"
    which: "npm:^1.2.9"
  checksum: 10c0/1918621fddb9f8c61e02118b2dbf81f611ccd1544ceaca0d026525341832b8511ce2504c60f935dbc06b35e5ef156fe8c1e72708c27dd486f034e9c0e1e07201
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10c0/80c089d6f7e0c5b2bd83cf0539ab41474198579584fa10d86d0cafe0642202343cbc119e076a0b1aece191989477081415d66c9fefbf3c957fc2fc4b7009f248
  languageName: node
  linkType: hard

"cytoscape-cose-bilkent@npm:^4.1.0":
  version: 4.1.0
  resolution: "cytoscape-cose-bilkent@npm:4.1.0"
  dependencies:
    cose-base: "npm:^1.0.0"
  peerDependencies:
    cytoscape: ^3.2.0
  checksum: 10c0/5e2480ddba9da1a68e700ed2c674cbfd51e9efdbd55788f1971a68de4eb30708e3b3a5e808bf5628f7a258680406bbe6586d87a9133e02a9bdc1ab1a92f512f2
  languageName: node
  linkType: hard

"cytoscape@npm:^3.28.1":
  version: 3.32.1
  resolution: "cytoscape@npm:3.32.1"
  checksum: 10c0/142419ddabcbddd999effcb55a3738229a77f75b6433fade1832236a78e098833d5a08240310c40cb836334a49ffa6e9a6b66db8b3530e58a3359c3a38441ae8
  languageName: node
  linkType: hard

"d3-array@npm:1 - 2":
  version: 2.12.1
  resolution: "d3-array@npm:2.12.1"
  dependencies:
    internmap: "npm:^1.0.0"
  checksum: 10c0/7eca10427a9f113a4ca6a0f7301127cab26043fd5e362631ef5a0edd1c4b2dd70c56ed317566700c31e4a6d88b55f3951aaba192291817f243b730cb2352882e
  languageName: node
  linkType: hard

"d3-array@npm:2 - 3, d3-array@npm:2.10.0 - 3, d3-array@npm:2.5.0 - 3, d3-array@npm:3, d3-array@npm:^3.2.0":
  version: 3.2.4
  resolution: "d3-array@npm:3.2.4"
  dependencies:
    internmap: "npm:1 - 2"
  checksum: 10c0/08b95e91130f98c1375db0e0af718f4371ccacef7d5d257727fe74f79a24383e79aba280b9ffae655483ffbbad4fd1dec4ade0119d88c4749f388641c8bf8c50
  languageName: node
  linkType: hard

"d3-axis@npm:3":
  version: 3.0.0
  resolution: "d3-axis@npm:3.0.0"
  checksum: 10c0/a271e70ba1966daa5aaf6a7f959ceca3e12997b43297e757c7b945db2e1ead3c6ee226f2abcfa22abbd4e2e28bd2b71a0911794c4e5b911bbba271328a582c78
  languageName: node
  linkType: hard

"d3-brush@npm:3":
  version: 3.0.0
  resolution: "d3-brush@npm:3.0.0"
  dependencies:
    d3-dispatch: "npm:1 - 3"
    d3-drag: "npm:2 - 3"
    d3-interpolate: "npm:1 - 3"
    d3-selection: "npm:3"
    d3-transition: "npm:3"
  checksum: 10c0/07baf00334c576da2f68a91fc0da5732c3a5fa19bd3d7aed7fd24d1d674a773f71a93e9687c154176f7246946194d77c48c2d8fed757f5dcb1a4740067ec50a8
  languageName: node
  linkType: hard

"d3-chord@npm:3":
  version: 3.0.1
  resolution: "d3-chord@npm:3.0.1"
  dependencies:
    d3-path: "npm:1 - 3"
  checksum: 10c0/baa6013914af3f4fe1521f0d16de31a38eb8a71d08ff1dec4741f6f45a828661e5cd3935e39bd14e3032bdc78206c283ca37411da21d46ec3cfc520be6e7a7ce
  languageName: node
  linkType: hard

"d3-color@npm:1 - 3, d3-color@npm:3":
  version: 3.1.0
  resolution: "d3-color@npm:3.1.0"
  checksum: 10c0/a4e20e1115fa696fce041fbe13fbc80dc4c19150fa72027a7c128ade980bc0eeeba4bcf28c9e21f0bce0e0dbfe7ca5869ef67746541dcfda053e4802ad19783c
  languageName: node
  linkType: hard

"d3-contour@npm:4":
  version: 4.0.2
  resolution: "d3-contour@npm:4.0.2"
  dependencies:
    d3-array: "npm:^3.2.0"
  checksum: 10c0/98bc5fbed6009e08707434a952076f39f1cd6ed8b9288253cc3e6a3286e4e80c63c62d84954b20e64bf6e4ededcc69add54d3db25e990784a59c04edd3449032
  languageName: node
  linkType: hard

"d3-delaunay@npm:6":
  version: 6.0.4
  resolution: "d3-delaunay@npm:6.0.4"
  dependencies:
    delaunator: "npm:5"
  checksum: 10c0/57c3aecd2525664b07c4c292aa11cf49b2752c0cf3f5257f752999399fe3c592de2d418644d79df1f255471eec8057a9cc0c3062ed7128cb3348c45f69597754
  languageName: node
  linkType: hard

"d3-dispatch@npm:1 - 3, d3-dispatch@npm:3":
  version: 3.0.1
  resolution: "d3-dispatch@npm:3.0.1"
  checksum: 10c0/6eca77008ce2dc33380e45d4410c67d150941df7ab45b91d116dbe6d0a3092c0f6ac184dd4602c796dc9e790222bad3ff7142025f5fd22694efe088d1d941753
  languageName: node
  linkType: hard

"d3-drag@npm:2 - 3, d3-drag@npm:3":
  version: 3.0.0
  resolution: "d3-drag@npm:3.0.0"
  dependencies:
    d3-dispatch: "npm:1 - 3"
    d3-selection: "npm:3"
  checksum: 10c0/d2556e8dc720741a443b595a30af403dd60642dfd938d44d6e9bfc4c71a962142f9a028c56b61f8b4790b65a34acad177d1263d66f103c3c527767b0926ef5aa
  languageName: node
  linkType: hard

"d3-dsv@npm:1 - 3, d3-dsv@npm:3":
  version: 3.0.1
  resolution: "d3-dsv@npm:3.0.1"
  dependencies:
    commander: "npm:7"
    iconv-lite: "npm:0.6"
    rw: "npm:1"
  bin:
    csv2json: bin/dsv2json.js
    csv2tsv: bin/dsv2dsv.js
    dsv2dsv: bin/dsv2dsv.js
    dsv2json: bin/dsv2json.js
    json2csv: bin/json2dsv.js
    json2dsv: bin/json2dsv.js
    json2tsv: bin/json2dsv.js
    tsv2csv: bin/dsv2dsv.js
    tsv2json: bin/dsv2json.js
  checksum: 10c0/10e6af9e331950ed258f34ab49ac1b7060128ef81dcf32afc790bd1f7e8c3cc2aac7f5f875250a83f21f39bb5925fbd0872bb209f8aca32b3b77d32bab8a65ab
  languageName: node
  linkType: hard

"d3-ease@npm:1 - 3, d3-ease@npm:3":
  version: 3.0.1
  resolution: "d3-ease@npm:3.0.1"
  checksum: 10c0/fec8ef826c0cc35cda3092c6841e07672868b1839fcaf556e19266a3a37e6bc7977d8298c0fcb9885e7799bfdcef7db1baaba9cd4dcf4bc5e952cf78574a88b0
  languageName: node
  linkType: hard

"d3-fetch@npm:3":
  version: 3.0.1
  resolution: "d3-fetch@npm:3.0.1"
  dependencies:
    d3-dsv: "npm:1 - 3"
  checksum: 10c0/4f467a79bf290395ac0cbb5f7562483f6a18668adc4c8eb84c9d3eff048b6f6d3b6f55079ba1ebf1908dabe000c941d46be447f8d78453b2dad5fb59fb6aa93b
  languageName: node
  linkType: hard

"d3-force@npm:3":
  version: 3.0.0
  resolution: "d3-force@npm:3.0.0"
  dependencies:
    d3-dispatch: "npm:1 - 3"
    d3-quadtree: "npm:1 - 3"
    d3-timer: "npm:1 - 3"
  checksum: 10c0/220a16a1a1ac62ba56df61028896e4b52be89c81040d20229c876efc8852191482c233f8a52bb5a4e0875c321b8e5cb6413ef3dfa4d8fe79eeb7d52c587f52cf
  languageName: node
  linkType: hard

"d3-format@npm:1 - 3, d3-format@npm:3":
  version: 3.1.0
  resolution: "d3-format@npm:3.1.0"
  checksum: 10c0/049f5c0871ebce9859fc5e2f07f336b3c5bfff52a2540e0bac7e703fce567cd9346f4ad1079dd18d6f1e0eaa0599941c1810898926f10ac21a31fd0a34b4aa75
  languageName: node
  linkType: hard

"d3-geo@npm:3":
  version: 3.1.1
  resolution: "d3-geo@npm:3.1.1"
  dependencies:
    d3-array: "npm:2.5.0 - 3"
  checksum: 10c0/d32270dd2dc8ac3ea63e8805d63239c4c8ec6c0d339d73b5e5a30a87f8f54db22a78fb434369799465eae169503b25f9a107c642c8a16c32a3285bc0e6d8e8c1
  languageName: node
  linkType: hard

"d3-hierarchy@npm:3":
  version: 3.1.2
  resolution: "d3-hierarchy@npm:3.1.2"
  checksum: 10c0/6dcdb480539644aa7fc0d72dfc7b03f99dfbcdf02714044e8c708577e0d5981deb9d3e99bbbb2d26422b55bcc342ac89a0fa2ea6c9d7302e2fc0951dd96f89cf
  languageName: node
  linkType: hard

"d3-interpolate@npm:1 - 3, d3-interpolate@npm:1.2.0 - 3, d3-interpolate@npm:3":
  version: 3.0.1
  resolution: "d3-interpolate@npm:3.0.1"
  dependencies:
    d3-color: "npm:1 - 3"
  checksum: 10c0/19f4b4daa8d733906671afff7767c19488f51a43d251f8b7f484d5d3cfc36c663f0a66c38fe91eee30f40327443d799be17169f55a293a3ba949e84e57a33e6a
  languageName: node
  linkType: hard

"d3-path@npm:1":
  version: 1.0.9
  resolution: "d3-path@npm:1.0.9"
  checksum: 10c0/e35e84df5abc18091f585725b8235e1fa97efc287571585427d3a3597301e6c506dea56b11dfb3c06ca5858b3eb7f02c1bf4f6a716aa9eade01c41b92d497eb5
  languageName: node
  linkType: hard

"d3-path@npm:1 - 3, d3-path@npm:3, d3-path@npm:^3.1.0":
  version: 3.1.0
  resolution: "d3-path@npm:3.1.0"
  checksum: 10c0/dc1d58ec87fa8319bd240cf7689995111a124b141428354e9637aa83059eb12e681f77187e0ada5dedfce346f7e3d1f903467ceb41b379bfd01cd8e31721f5da
  languageName: node
  linkType: hard

"d3-polygon@npm:3":
  version: 3.0.1
  resolution: "d3-polygon@npm:3.0.1"
  checksum: 10c0/e236aa7f33efa9a4072907af7dc119f85b150a0716759d4fe5f12f62573018264a6cbde8617fbfa6944a7ae48c1c0c8d3f39ae72e11f66dd471e9b5e668385df
  languageName: node
  linkType: hard

"d3-quadtree@npm:1 - 3, d3-quadtree@npm:3":
  version: 3.0.1
  resolution: "d3-quadtree@npm:3.0.1"
  checksum: 10c0/18302d2548bfecaef788152397edec95a76400fd97d9d7f42a089ceb68d910f685c96579d74e3712d57477ed042b056881b47cd836a521de683c66f47ce89090
  languageName: node
  linkType: hard

"d3-random@npm:3":
  version: 3.0.1
  resolution: "d3-random@npm:3.0.1"
  checksum: 10c0/987a1a1bcbf26e6cf01fd89d5a265b463b2cea93560fc17d9b1c45e8ed6ff2db5924601bcceb808de24c94133f000039eb7fa1c469a7a844ccbf1170cbb25b41
  languageName: node
  linkType: hard

"d3-sankey@npm:^0.12.3":
  version: 0.12.3
  resolution: "d3-sankey@npm:0.12.3"
  dependencies:
    d3-array: "npm:1 - 2"
    d3-shape: "npm:^1.2.0"
  checksum: 10c0/261debb01a13269f6fc53b9ebaef174a015d5ad646242c23995bf514498829ab8b8f920a7873724a7494288b46bea3ce7ebc5a920b745bc8ae4caa5885cf5204
  languageName: node
  linkType: hard

"d3-scale-chromatic@npm:3":
  version: 3.1.0
  resolution: "d3-scale-chromatic@npm:3.1.0"
  dependencies:
    d3-color: "npm:1 - 3"
    d3-interpolate: "npm:1 - 3"
  checksum: 10c0/9a3f4671ab0b971f4a411b42180d7cf92bfe8e8584e637ce7e698d705e18d6d38efbd20ec64f60cc0dfe966c20d40fc172565bc28aaa2990c0a006360eed91af
  languageName: node
  linkType: hard

"d3-scale@npm:4":
  version: 4.0.2
  resolution: "d3-scale@npm:4.0.2"
  dependencies:
    d3-array: "npm:2.10.0 - 3"
    d3-format: "npm:1 - 3"
    d3-interpolate: "npm:1.2.0 - 3"
    d3-time: "npm:2.1.1 - 3"
    d3-time-format: "npm:2 - 4"
  checksum: 10c0/65d9ad8c2641aec30ed5673a7410feb187a224d6ca8d1a520d68a7d6eac9d04caedbff4713d1e8545be33eb7fec5739983a7ab1d22d4e5ad35368c6729d362f1
  languageName: node
  linkType: hard

"d3-selection@npm:2 - 3, d3-selection@npm:3":
  version: 3.0.0
  resolution: "d3-selection@npm:3.0.0"
  checksum: 10c0/e59096bbe8f0cb0daa1001d9bdd6dbc93a688019abc97d1d8b37f85cd3c286a6875b22adea0931b0c88410d025563e1643019161a883c516acf50c190a11b56b
  languageName: node
  linkType: hard

"d3-shape@npm:3":
  version: 3.2.0
  resolution: "d3-shape@npm:3.2.0"
  dependencies:
    d3-path: "npm:^3.1.0"
  checksum: 10c0/f1c9d1f09926daaf6f6193ae3b4c4b5521e81da7d8902d24b38694517c7f527ce3c9a77a9d3a5722ad1e3ff355860b014557b450023d66a944eabf8cfde37132
  languageName: node
  linkType: hard

"d3-shape@npm:^1.2.0":
  version: 1.3.7
  resolution: "d3-shape@npm:1.3.7"
  dependencies:
    d3-path: "npm:1"
  checksum: 10c0/548057ce59959815decb449f15632b08e2a1bdce208f9a37b5f98ec7629dda986c2356bc7582308405ce68aedae7d47b324df41507404df42afaf352907577ae
  languageName: node
  linkType: hard

"d3-time-format@npm:2 - 4, d3-time-format@npm:4":
  version: 4.1.0
  resolution: "d3-time-format@npm:4.1.0"
  dependencies:
    d3-time: "npm:1 - 3"
  checksum: 10c0/735e00fb25a7fd5d418fac350018713ae394eefddb0d745fab12bbff0517f9cdb5f807c7bbe87bb6eeb06249662f8ea84fec075f7d0cd68609735b2ceb29d206
  languageName: node
  linkType: hard

"d3-time@npm:1 - 3, d3-time@npm:2.1.1 - 3, d3-time@npm:3":
  version: 3.1.0
  resolution: "d3-time@npm:3.1.0"
  dependencies:
    d3-array: "npm:2 - 3"
  checksum: 10c0/a984f77e1aaeaa182679b46fbf57eceb6ebdb5f67d7578d6f68ef933f8eeb63737c0949991618a8d29472dbf43736c7d7f17c452b2770f8c1271191cba724ca1
  languageName: node
  linkType: hard

"d3-timer@npm:1 - 3, d3-timer@npm:3":
  version: 3.0.1
  resolution: "d3-timer@npm:3.0.1"
  checksum: 10c0/d4c63cb4bb5461d7038aac561b097cd1c5673969b27cbdd0e87fa48d9300a538b9e6f39b4a7f0e3592ef4f963d858c8a9f0e92754db73116770856f2fc04561a
  languageName: node
  linkType: hard

"d3-transition@npm:2 - 3, d3-transition@npm:3":
  version: 3.0.1
  resolution: "d3-transition@npm:3.0.1"
  dependencies:
    d3-color: "npm:1 - 3"
    d3-dispatch: "npm:1 - 3"
    d3-ease: "npm:1 - 3"
    d3-interpolate: "npm:1 - 3"
    d3-timer: "npm:1 - 3"
  peerDependencies:
    d3-selection: 2 - 3
  checksum: 10c0/4e74535dda7024aa43e141635b7522bb70cf9d3dfefed975eb643b36b864762eca67f88fafc2ca798174f83ca7c8a65e892624f824b3f65b8145c6a1a88dbbad
  languageName: node
  linkType: hard

"d3-zoom@npm:3":
  version: 3.0.0
  resolution: "d3-zoom@npm:3.0.0"
  dependencies:
    d3-dispatch: "npm:1 - 3"
    d3-drag: "npm:2 - 3"
    d3-interpolate: "npm:1 - 3"
    d3-selection: "npm:2 - 3"
    d3-transition: "npm:2 - 3"
  checksum: 10c0/ee2036479049e70d8c783d594c444fe00e398246048e3f11a59755cd0e21de62ece3126181b0d7a31bf37bcf32fd726f83ae7dea4495ff86ec7736ce5ad36fd3
  languageName: node
  linkType: hard

"d3@npm:^7.4.0, d3@npm:^7.8.2":
  version: 7.9.0
  resolution: "d3@npm:7.9.0"
  dependencies:
    d3-array: "npm:3"
    d3-axis: "npm:3"
    d3-brush: "npm:3"
    d3-chord: "npm:3"
    d3-color: "npm:3"
    d3-contour: "npm:4"
    d3-delaunay: "npm:6"
    d3-dispatch: "npm:3"
    d3-drag: "npm:3"
    d3-dsv: "npm:3"
    d3-ease: "npm:3"
    d3-fetch: "npm:3"
    d3-force: "npm:3"
    d3-format: "npm:3"
    d3-geo: "npm:3"
    d3-hierarchy: "npm:3"
    d3-interpolate: "npm:3"
    d3-path: "npm:3"
    d3-polygon: "npm:3"
    d3-quadtree: "npm:3"
    d3-random: "npm:3"
    d3-scale: "npm:4"
    d3-scale-chromatic: "npm:3"
    d3-selection: "npm:3"
    d3-shape: "npm:3"
    d3-time: "npm:3"
    d3-time-format: "npm:4"
    d3-timer: "npm:3"
    d3-transition: "npm:3"
    d3-zoom: "npm:3"
  checksum: 10c0/3dd9c08c73cfaa69c70c49e603c85e049c3904664d9c79a1a52a0f52795828a1ff23592dc9a7b2257e711d68a615472a13103c212032f38e016d609796e087e8
  languageName: node
  linkType: hard

"dagre-d3-es@npm:7.0.10":
  version: 7.0.10
  resolution: "dagre-d3-es@npm:7.0.10"
  dependencies:
    d3: "npm:^7.8.2"
    lodash-es: "npm:^4.17.21"
  checksum: 10c0/3e1bb6efe9a78cea3fe6ff265eb330692f057bf84c99d6a1d67db379231c37a1a1ca2e1ccc25a732ddf924cd5566062c033d88defd230debec324dc9256c6775
  languageName: node
  linkType: hard

"dayjs@npm:^1.11.7":
  version: 1.11.13
  resolution: "dayjs@npm:1.11.13"
  checksum: 10c0/a3caf6ac8363c7dade9d1ee797848ddcf25c1ace68d9fe8678ecf8ba0675825430de5d793672ec87b24a69bf04a1544b176547b2539982275d5542a7955f35b7
  languageName: node
  linkType: hard

"debug@npm:^4.0.0":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/d2b44bc1afd912b49bb7ebb0d50a860dc93a4dd7d946e8de94abc957bb63726b7dd5aa48c18c2386c379ec024c46692e15ed3ed97d481729f929201e671fcd55
  languageName: node
  linkType: hard

"decode-named-character-reference@npm:^1.0.0":
  version: 1.2.0
  resolution: "decode-named-character-reference@npm:1.2.0"
  dependencies:
    character-entities: "npm:^2.0.0"
  checksum: 10c0/761a89de6b0e0a2d4b21ae99074e4cc3344dd11eb29f112e23cc5909f2e9f33c5ed20cd6b146b27fb78170bce0f3f9b3362a84b75638676a05c938c24a60f5d7
  languageName: node
  linkType: hard

"delaunator@npm:5":
  version: 5.0.1
  resolution: "delaunator@npm:5.0.1"
  dependencies:
    robust-predicates: "npm:^3.0.2"
  checksum: 10c0/3d7ea4d964731c5849af33fec0a271bc6753487b331fd7d43ccb17d77834706e1c383e6ab8fda0032da955e7576d1083b9603cdaf9cbdfd6b3ebd1fb8bb675a5
  languageName: node
  linkType: hard

"dequal@npm:^2.0.0":
  version: 2.0.3
  resolution: "dequal@npm:2.0.3"
  checksum: 10c0/f98860cdf58b64991ae10205137c0e97d384c3a4edc7f807603887b7c4b850af1224a33d88012009f150861cbee4fa2d322c4cc04b9313bee312e47f6ecaa888
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.4":
  version: 2.0.4
  resolution: "detect-libc@npm:2.0.4"
  checksum: 10c0/c15541f836eba4b1f521e4eecc28eefefdbc10a94d3b8cb4c507689f332cc111babb95deda66f2de050b22122113189986d5190be97d51b5a2b23b938415e67c
  languageName: node
  linkType: hard

"devlop@npm:^1.0.0, devlop@npm:^1.1.0":
  version: 1.1.0
  resolution: "devlop@npm:1.1.0"
  dependencies:
    dequal: "npm:^2.0.0"
  checksum: 10c0/e0928ab8f94c59417a2b8389c45c55ce0a02d9ac7fd74ef62d01ba48060129e1d594501b77de01f3eeafc7cb00773819b0df74d96251cf20b31c5b3071f45c0e
  languageName: node
  linkType: hard

"diff@npm:^5.0.0":
  version: 5.2.0
  resolution: "diff@npm:5.2.0"
  checksum: 10c0/aed0941f206fe261ecb258dc8d0ceea8abbde3ace5827518ff8d302f0fc9cc81ce116c4d8f379151171336caf0516b79e01abdc1ed1201b6440d895a66689eb4
  languageName: node
  linkType: hard

"dompurify@npm:^3.0.5 <3.1.7":
  version: 3.1.6
  resolution: "dompurify@npm:3.1.6"
  checksum: 10c0/3de1cca187c78d3d8cb4134fc2985b644d6a81f6b4e024c77cfb04c1c2f38544ccf7b0ea37a48ce22fcca64594170ed7c22252574c75b801c44345cdd7b06c64
  languageName: node
  linkType: hard

"elkjs@npm:^0.9.0":
  version: 0.9.3
  resolution: "elkjs@npm:0.9.3"
  checksum: 10c0/caf544ff4fce8442d1d3dd6dface176c9b2fe26fc1e34f56122828e6eef7d2d7fe70d3202f9f3ecf0feb6287d4c8430949f483e63e450a7454bb39ccffab3808
  languageName: node
  linkType: hard

"entities@npm:^6.0.0":
  version: 6.0.1
  resolution: "entities@npm:6.0.1"
  checksum: 10c0/ed836ddac5acb34341094eb495185d527bd70e8632b6c0d59548cbfa23defdbae70b96f9a405c82904efa421230b5b3fd2283752447d737beffd3f3e6ee74414
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 10c0/a968ad453dd0c2724e14a4f20e177aaf32bb384ab41b674a8454afe9a41c5e6fe8903323e0a1052f56289d04bd600f81278edf140b0fcc02f5cac98d0f5b5371
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^5.0.0":
  version: 5.0.0
  resolution: "escape-string-regexp@npm:5.0.0"
  checksum: 10c0/6366f474c6f37a802800a435232395e04e9885919873e382b157ab7e8f0feb8fed71497f84a6f6a81a49aab41815522f5839112bd38026d203aea0c91622df95
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10c0/ad4bab9ead0808cf56501750fd9d3fb276f6b105f987707d059005d57e182d18a7c9ec7f3a01794ebddcca676773e42ca48a32d67a250c9d35e009ca613caba3
  languageName: node
  linkType: hard

"estree-util-attach-comments@npm:^2.0.0":
  version: 2.1.1
  resolution: "estree-util-attach-comments@npm:2.1.1"
  dependencies:
    "@types/estree": "npm:^1.0.0"
  checksum: 10c0/cdb5fdb5809b376ca4a96afbcd916c3570b4bbf5d0115b8a9e1e8a10885d8d9fb549df0a16c077abb42ee35fa33192b69714bac25d4f3c43a36092288c9a64fd
  languageName: node
  linkType: hard

"estree-util-build-jsx@npm:^2.0.0":
  version: 2.2.2
  resolution: "estree-util-build-jsx@npm:2.2.2"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    estree-util-is-identifier-name: "npm:^2.0.0"
    estree-walker: "npm:^3.0.0"
  checksum: 10c0/2cef6ad6747f51934eba0601c3477ba08c98331cfe616635e08dfc89d06b9bbd370c4d80e87fe7d42d82776fa7840868201f48491b0ef9c808039f15fe4667e1
  languageName: node
  linkType: hard

"estree-util-is-identifier-name@npm:^2.0.0":
  version: 2.1.0
  resolution: "estree-util-is-identifier-name@npm:2.1.0"
  checksum: 10c0/cc241a6998d30f4e8775ec34b042ef93e0085cd1bdf692a01f22e9b748f0866c76679475ff87935be1d8d5b1a7648be8cba366dc60866b372269f35feec756fe
  languageName: node
  linkType: hard

"estree-util-to-js@npm:^1.1.0":
  version: 1.2.0
  resolution: "estree-util-to-js@npm:1.2.0"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    astring: "npm:^1.8.0"
    source-map: "npm:^0.7.0"
  checksum: 10c0/ad9c99dc34b0510ab813b485251acbf0abd06361c07b13c08da5d1611c279bee02ec09f2c269ae30b8d2da587115fc1fad4fa9f2f5ba69e094e758a3a4de7069
  languageName: node
  linkType: hard

"estree-util-value-to-estree@npm:^3.3.3":
  version: 3.4.0
  resolution: "estree-util-value-to-estree@npm:3.4.0"
  dependencies:
    "@types/estree": "npm:^1.0.0"
  checksum: 10c0/e90e0c784b29182a3feb471589ab3c031be3ff1ab068b2b473e9ee96467f99442f2c571b2708ee3493906af5bf1a0aa9712d9f90fb113a30d99669100235ba4f
  languageName: node
  linkType: hard

"estree-util-visit@npm:^1.0.0":
  version: 1.2.1
  resolution: "estree-util-visit@npm:1.2.1"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/unist": "npm:^2.0.0"
  checksum: 10c0/3c47086ab25947a889fca9f58a842e0d27edadcad24dc393fdd7c9ad3419fe05b3c63b6fc9d6c9d8f50d32bca615cd0a3fe8d0e6b300fb94f74c91210b55ea5d
  languageName: node
  linkType: hard

"estree-walker@npm:^3.0.0":
  version: 3.0.3
  resolution: "estree-walker@npm:3.0.3"
  dependencies:
    "@types/estree": "npm:^1.0.0"
  checksum: 10c0/c12e3c2b2642d2bcae7d5aa495c60fa2f299160946535763969a1c83fc74518ffa9c2cd3a8b69ac56aea547df6a8aac25f729a342992ef0bbac5f1c73e78995d
  languageName: node
  linkType: hard

"execa@npm:^0.8.0":
  version: 0.8.0
  resolution: "execa@npm:0.8.0"
  dependencies:
    cross-spawn: "npm:^5.0.1"
    get-stream: "npm:^3.0.0"
    is-stream: "npm:^1.1.0"
    npm-run-path: "npm:^2.0.0"
    p-finally: "npm:^1.0.0"
    signal-exit: "npm:^3.0.0"
    strip-eof: "npm:^1.0.0"
  checksum: 10c0/e6c085687024cd5d348cad98a12213f6ebad2e962c7f3298ea8608fd5ed2daad8d1e27e79bfe7104bf60d8d80b56dd60267a0667006c29019e4297c96ecfe99d
  languageName: node
  linkType: hard

"extend-shallow@npm:^2.0.1":
  version: 2.0.1
  resolution: "extend-shallow@npm:2.0.1"
  dependencies:
    is-extendable: "npm:^0.1.0"
  checksum: 10c0/ee1cb0a18c9faddb42d791b2d64867bd6cfd0f3affb711782eb6e894dd193e2934a7f529426aac7c8ddb31ac5d38000a00aa2caf08aa3dfc3e1c8ff6ba340bd9
  languageName: node
  linkType: hard

"extend@npm:^3.0.0":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: 10c0/73bf6e27406e80aa3e85b0d1c4fd987261e628064e170ca781125c0b635a3dabad5e05adbf07595ea0cf1e6c5396cacb214af933da7cbaf24fe75ff14818e8f9
  languageName: node
  linkType: hard

"get-stream@npm:^3.0.0":
  version: 3.0.0
  resolution: "get-stream@npm:3.0.0"
  checksum: 10c0/003f5f3b8870da59c6aafdf6ed7e7b07b48c2f8629cd461bd3900726548b6b8cfa2e14d6b7814fbb08f07a42f4f738407fa70b989928b2783a76b278505bba22
  languageName: node
  linkType: hard

"github-slugger@npm:^2.0.0":
  version: 2.0.0
  resolution: "github-slugger@npm:2.0.0"
  checksum: 10c0/21b912b6b1e48f1e5a50b2292b48df0ff6abeeb0691b161b3d93d84f4ae6b1acd6ae23702e914af7ea5d441c096453cf0f621b72d57893946618d21dd1a1c486
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.2.11":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"gray-matter@npm:^4.0.3":
  version: 4.0.3
  resolution: "gray-matter@npm:4.0.3"
  dependencies:
    js-yaml: "npm:^3.13.1"
    kind-of: "npm:^6.0.2"
    section-matter: "npm:^1.0.0"
    strip-bom-string: "npm:^1.0.0"
  checksum: 10c0/e38489906dad4f162ca01e0dcbdbed96d1a53740cef446b9bf76d80bec66fa799af07776a18077aee642346c5e1365ed95e4c91854a12bf40ba0d4fb43a625a6
  languageName: node
  linkType: hard

"has-flag@npm:^2.0.0":
  version: 2.0.0
  resolution: "has-flag@npm:2.0.0"
  checksum: 10c0/5e1f136c7f801c2719048bedfabcf834a1ed46276cd4c98c6fcddb89a482f5d6a16df0771a38805cfc2d9010b4de157909e1a71b708e1d339b6e311041bde9b4
  languageName: node
  linkType: hard

"hash-obj@npm:^4.0.0":
  version: 4.0.0
  resolution: "hash-obj@npm:4.0.0"
  dependencies:
    is-obj: "npm:^3.0.0"
    sort-keys: "npm:^5.0.0"
    type-fest: "npm:^1.0.2"
  checksum: 10c0/af0a8bd3905afa2b9bd05ec75e37d904c66f6621ae185d53699fc7e5baf8157aeff6f4b9ae3c579da08aae6a5b2536c445c4dd1eecb94070c8717b63eeca97de
  languageName: node
  linkType: hard

"hast-util-from-dom@npm:^5.0.0":
  version: 5.0.1
  resolution: "hast-util-from-dom@npm:5.0.1"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    hastscript: "npm:^9.0.0"
    web-namespaces: "npm:^2.0.0"
  checksum: 10c0/9a90381e048107a093a3da758bb17b67aaf5322e222f02497f841c4990abf94aa177d38d5b9bf61ad07b3601d0409f34f5b556d89578cc189230c6b994d2af77
  languageName: node
  linkType: hard

"hast-util-from-html-isomorphic@npm:^2.0.0":
  version: 2.0.0
  resolution: "hast-util-from-html-isomorphic@npm:2.0.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    hast-util-from-dom: "npm:^5.0.0"
    hast-util-from-html: "npm:^2.0.0"
    unist-util-remove-position: "npm:^5.0.0"
  checksum: 10c0/fc68d9245e794483a802d5c85a9f6c25959e00db78cc796411efc965134f3206f9cc9fa38134572ea781ad74663e801f1f83202007b208e27a770855566a62b6
  languageName: node
  linkType: hard

"hast-util-from-html@npm:^2.0.0":
  version: 2.0.3
  resolution: "hast-util-from-html@npm:2.0.3"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    devlop: "npm:^1.1.0"
    hast-util-from-parse5: "npm:^8.0.0"
    parse5: "npm:^7.0.0"
    vfile: "npm:^6.0.0"
    vfile-message: "npm:^4.0.0"
  checksum: 10c0/993ef707c1a12474c8d4094fc9706a72826c660a7e308ea54c50ad893353d32e139b7cbc67510c2e82feac572b320e3b05aeb13d0f9c6302d61261f337b46764
  languageName: node
  linkType: hard

"hast-util-from-parse5@npm:^8.0.0":
  version: 8.0.3
  resolution: "hast-util-from-parse5@npm:8.0.3"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/unist": "npm:^3.0.0"
    devlop: "npm:^1.0.0"
    hastscript: "npm:^9.0.0"
    property-information: "npm:^7.0.0"
    vfile: "npm:^6.0.0"
    vfile-location: "npm:^5.0.0"
    web-namespaces: "npm:^2.0.0"
  checksum: 10c0/40ace6c0ad43c26f721c7499fe408e639cde917b2350c9299635e6326559855896dae3c3ebf7440df54766b96c4276a7823e8f376a2b6a28b37b591f03412545
  languageName: node
  linkType: hard

"hast-util-is-element@npm:^3.0.0":
  version: 3.0.0
  resolution: "hast-util-is-element@npm:3.0.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
  checksum: 10c0/f5361e4c9859c587ca8eb0d8343492f3077ccaa0f58a44cd09f35d5038f94d65152288dcd0c19336ef2c9491ec4d4e45fde2176b05293437021570aa0bc3613b
  languageName: node
  linkType: hard

"hast-util-parse-selector@npm:^4.0.0":
  version: 4.0.0
  resolution: "hast-util-parse-selector@npm:4.0.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
  checksum: 10c0/5e98168cb44470dc274aabf1a28317e4feb09b1eaf7a48bbaa8c1de1b43a89cd195cb1284e535698e658e3ec26ad91bc5e52c9563c36feb75abbc68aaf68fb9f
  languageName: node
  linkType: hard

"hast-util-raw@npm:^9.0.0":
  version: 9.1.0
  resolution: "hast-util-raw@npm:9.1.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/unist": "npm:^3.0.0"
    "@ungap/structured-clone": "npm:^1.0.0"
    hast-util-from-parse5: "npm:^8.0.0"
    hast-util-to-parse5: "npm:^8.0.0"
    html-void-elements: "npm:^3.0.0"
    mdast-util-to-hast: "npm:^13.0.0"
    parse5: "npm:^7.0.0"
    unist-util-position: "npm:^5.0.0"
    unist-util-visit: "npm:^5.0.0"
    vfile: "npm:^6.0.0"
    web-namespaces: "npm:^2.0.0"
    zwitch: "npm:^2.0.0"
  checksum: 10c0/d0d909d2aedecef6a06f0005cfae410d6475e6e182d768bde30c3af9fcbbe4f9beb0522bdc21d0679cb3c243c0df40385797ed255148d68b3d3f12e82d12aacc
  languageName: node
  linkType: hard

"hast-util-to-estree@npm:^2.0.0":
  version: 2.3.3
  resolution: "hast-util-to-estree@npm:2.3.3"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/hast": "npm:^2.0.0"
    "@types/unist": "npm:^2.0.0"
    comma-separated-tokens: "npm:^2.0.0"
    estree-util-attach-comments: "npm:^2.0.0"
    estree-util-is-identifier-name: "npm:^2.0.0"
    hast-util-whitespace: "npm:^2.0.0"
    mdast-util-mdx-expression: "npm:^1.0.0"
    mdast-util-mdxjs-esm: "npm:^1.0.0"
    property-information: "npm:^6.0.0"
    space-separated-tokens: "npm:^2.0.0"
    style-to-object: "npm:^0.4.1"
    unist-util-position: "npm:^4.0.0"
    zwitch: "npm:^2.0.0"
  checksum: 10c0/5947b5030a6d20c193f5ea576cc751507e0b30d00f91e40a5208ca3a7add03a3862795a83600c0fdadf19c8b051917c7904715fa7dd358f04603d67a36341c38
  languageName: node
  linkType: hard

"hast-util-to-parse5@npm:^8.0.0":
  version: 8.0.0
  resolution: "hast-util-to-parse5@npm:8.0.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    comma-separated-tokens: "npm:^2.0.0"
    devlop: "npm:^1.0.0"
    property-information: "npm:^6.0.0"
    space-separated-tokens: "npm:^2.0.0"
    web-namespaces: "npm:^2.0.0"
    zwitch: "npm:^2.0.0"
  checksum: 10c0/3c0c7fba026e0c4be4675daf7277f9ff22ae6da801435f1b7104f7740de5422576f1c025023c7b3df1d0a161e13a04c6ab8f98ada96eb50adb287b537849a2bd
  languageName: node
  linkType: hard

"hast-util-to-text@npm:^4.0.0":
  version: 4.0.2
  resolution: "hast-util-to-text@npm:4.0.2"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/unist": "npm:^3.0.0"
    hast-util-is-element: "npm:^3.0.0"
    unist-util-find-after: "npm:^5.0.0"
  checksum: 10c0/93ecc10e68fe5391c6e634140eb330942e71dea2724c8e0c647c73ed74a8ec930a4b77043b5081284808c96f73f2bee64ee416038ece75a63a467e8d14f09946
  languageName: node
  linkType: hard

"hast-util-whitespace@npm:^2.0.0":
  version: 2.0.1
  resolution: "hast-util-whitespace@npm:2.0.1"
  checksum: 10c0/dcf6ebab091c802ffa7bb3112305c7631c15adb6c07a258f5528aefbddf82b4e162c8310ef426c48dc1dc623982cc33920e6dde5a50015d307f2778dcf6c2487
  languageName: node
  linkType: hard

"hastscript@npm:^9.0.0":
  version: 9.0.1
  resolution: "hastscript@npm:9.0.1"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    comma-separated-tokens: "npm:^2.0.0"
    hast-util-parse-selector: "npm:^4.0.0"
    property-information: "npm:^7.0.0"
    space-separated-tokens: "npm:^2.0.0"
  checksum: 10c0/18dc8064e5c3a7a2ae862978e626b97a254e1c8a67ee9d0c9f06d373bba155ed805fc5b5ce21b990fb7bc174624889e5e1ce1cade264f1b1d58b48f994bc85ce
  languageName: node
  linkType: hard

"html-void-elements@npm:^3.0.0":
  version: 3.0.0
  resolution: "html-void-elements@npm:3.0.0"
  checksum: 10c0/a8b9ec5db23b7c8053876dad73a0336183e6162bf6d2677376d8b38d654fdc59ba74fdd12f8812688f7db6fad451210c91b300e472afc0909224e0a44c8610d2
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"inline-style-parser@npm:0.1.1":
  version: 0.1.1
  resolution: "inline-style-parser@npm:0.1.1"
  checksum: 10c0/08832a533f51a1e17619f2eabf2f5ec5e956d6dcba1896351285c65df022c9420de61d73256e1dca8015a52abf96cc84ddc3b73b898b22de6589d3962b5e501b
  languageName: node
  linkType: hard

"internmap@npm:1 - 2":
  version: 2.0.3
  resolution: "internmap@npm:2.0.3"
  checksum: 10c0/8cedd57f07bbc22501516fbfc70447f0c6812871d471096fad9ea603516eacc2137b633633daf432c029712df0baefd793686388ddf5737e3ea15074b877f7ed
  languageName: node
  linkType: hard

"internmap@npm:^1.0.0":
  version: 1.0.1
  resolution: "internmap@npm:1.0.1"
  checksum: 10c0/60942be815ca19da643b6d4f23bd0bf4e8c97abbd080fb963fe67583b60bdfb3530448ad4486bae40810e92317bded9995cc31411218acc750d72cd4e8646eee
  languageName: node
  linkType: hard

"is-alphabetical@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-alphabetical@npm:2.0.1"
  checksum: 10c0/932367456f17237533fd1fc9fe179df77957271020b83ea31da50e5cc472d35ef6b5fb8147453274ffd251134472ce24eb6f8d8398d96dee98237cdb81a6c9a7
  languageName: node
  linkType: hard

"is-alphanumerical@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-alphanumerical@npm:2.0.1"
  dependencies:
    is-alphabetical: "npm:^2.0.0"
    is-decimal: "npm:^2.0.0"
  checksum: 10c0/4b35c42b18e40d41378293f82a3ecd9de77049b476f748db5697c297f686e1e05b072a6aaae2d16f54d2a57f85b00cbbe755c75f6d583d1c77d6657bd0feb5a2
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 10c0/f59b43dc1d129edb6f0e282595e56477f98c40278a2acdc8b0a5c57097c9eff8fe55470493df5775478cf32a4dc8eaf6d3a749f07ceee5bc263a78b2434f6a54
  languageName: node
  linkType: hard

"is-buffer@npm:^2.0.0":
  version: 2.0.5
  resolution: "is-buffer@npm:2.0.5"
  checksum: 10c0/e603f6fced83cf94c53399cff3bda1a9f08e391b872b64a73793b0928be3e5f047f2bcece230edb7632eaea2acdbfcb56c23b33d8a20c820023b230f1485679a
  languageName: node
  linkType: hard

"is-decimal@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-decimal@npm:2.0.1"
  checksum: 10c0/8085dd66f7d82f9de818fba48b9e9c0429cb4291824e6c5f2622e96b9680b54a07a624cfc663b24148b8e853c62a1c987cfe8b0b5a13f5156991afaf6736e334
  languageName: node
  linkType: hard

"is-extendable@npm:^0.1.0":
  version: 0.1.1
  resolution: "is-extendable@npm:0.1.1"
  checksum: 10c0/dd5ca3994a28e1740d1e25192e66eed128e0b2ff161a7ea348e87ae4f616554b486854de423877a2a2c171d5f7cd6e8093b91f54533bc88a59ee1c9838c43879
  languageName: node
  linkType: hard

"is-hexadecimal@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-hexadecimal@npm:2.0.1"
  checksum: 10c0/3eb60fe2f1e2bbc760b927dcad4d51eaa0c60138cf7fc671803f66353ad90c301605b502c7ea4c6bb0548e1c7e79dfd37b73b632652e3b76030bba603a7e9626
  languageName: node
  linkType: hard

"is-obj@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-obj@npm:3.0.0"
  checksum: 10c0/48d678fa15c56fd38353634ae2106a538827af9050211b18df13540dba0b38aa25c5cb498648a01311bf493a99ac3ce416576649b8cace10bcce7344611fa56a
  languageName: node
  linkType: hard

"is-plain-obj@npm:^4.0.0":
  version: 4.1.0
  resolution: "is-plain-obj@npm:4.1.0"
  checksum: 10c0/32130d651d71d9564dc88ba7e6fda0e91a1010a3694648e9f4f47bb6080438140696d3e3e15c741411d712e47ac9edc1a8a9de1fe76f3487b0d90be06ac9975e
  languageName: node
  linkType: hard

"is-reference@npm:^3.0.0":
  version: 3.0.3
  resolution: "is-reference@npm:3.0.3"
  dependencies:
    "@types/estree": "npm:^1.0.6"
  checksum: 10c0/35edd284cfb4cd9e9f08973f20e276ec517eaca31f5f049598e97dbb2d05544973dde212dac30fddee5b420930bff365e2e67dcd1293d0866c6720377382e3e5
  languageName: node
  linkType: hard

"is-stream@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-stream@npm:1.1.0"
  checksum: 10c0/b8ae7971e78d2e8488d15f804229c6eed7ed36a28f8807a1815938771f4adff0e705218b7dab968270433f67103e4fef98062a0beea55d64835f705ee72c7002
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10c0/e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: "npm:^1.0.7"
    esprima: "npm:^4.0.0"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/6746baaaeac312c4db8e75fa22331d9a04cccb7792d126ed8ce6a0bbcfef0cedaddd0c5098fade53db067c09fe00aa1c957674b4765610a8b06a5a189e46433b
  languageName: node
  linkType: hard

"js-yaml@npm:^4.0.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"jsonc-parser@npm:^3.2.0":
  version: 3.3.1
  resolution: "jsonc-parser@npm:3.3.1"
  checksum: 10c0/269c3ae0a0e4f907a914bf334306c384aabb9929bd8c99f909275ebd5c2d3bc70b9bcd119ad794f339dec9f24b6a4ee9cd5a8ab2e6435e730ad4075388fc2ab6
  languageName: node
  linkType: hard

"katex@npm:^0.16.0, katex@npm:^0.16.9":
  version: 0.16.22
  resolution: "katex@npm:0.16.22"
  dependencies:
    commander: "npm:^8.3.0"
  bin:
    katex: cli.js
  checksum: 10c0/07b8b1f07ae53171b5f1ea0cf6f18841d2055825c8b11cd81cfe039afcd3af2cfc84ad033531ee3875088329105195b039c267e0dd4b0c237807e3c3b2009913
  languageName: node
  linkType: hard

"khroma@npm:^2.0.0":
  version: 2.1.0
  resolution: "khroma@npm:2.1.0"
  checksum: 10c0/634d98753ff5d2540491cafeb708fc98de0d43f4e6795256d5c8f6e3ad77de93049ea41433928fda3697adf7bbe6fe27351858f6d23b78f8b5775ef314c59891
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.0, kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 10c0/61cdff9623dabf3568b6445e93e31376bee1cdb93f8ba7033d86022c2a9b1791a1d9510e026e6465ebd701a6dd2f7b0808483ad8838341ac52f003f512e0b4c4
  languageName: node
  linkType: hard

"kleur@npm:^4.0.3":
  version: 4.1.5
  resolution: "kleur@npm:4.1.5"
  checksum: 10c0/e9de6cb49657b6fa70ba2d1448fd3d691a5c4370d8f7bbf1c2f64c24d461270f2117e1b0afe8cb3114f13bbd8e51de158c2a224953960331904e636a5e4c0f2a
  languageName: node
  linkType: hard

"layout-base@npm:^1.0.0":
  version: 1.0.2
  resolution: "layout-base@npm:1.0.2"
  checksum: 10c0/2a55d0460fd9f6ed53d7e301b9eb3dea19bda03815d616a40665ce6dc75c1f4d62e1ca19a897da1cfaf6de1b91de59cd6f2f79ba1258f3d7fccc7d46ca7f3337
  languageName: node
  linkType: hard

"lodash-es@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash-es@npm:4.17.21"
  checksum: 10c0/fb407355f7e6cd523a9383e76e6b455321f0f153a6c9625e21a8827d10c54c2a2341bd2ae8d034358b60e07325e1330c14c224ff582d04612a46a4f0479ff2f2
  languageName: node
  linkType: hard

"lodash.get@npm:^4.4.2":
  version: 4.4.2
  resolution: "lodash.get@npm:4.4.2"
  checksum: 10c0/48f40d471a1654397ed41685495acb31498d5ed696185ac8973daef424a749ca0c7871bf7b665d5c14f5cc479394479e0307e781f61d5573831769593411be6e
  languageName: node
  linkType: hard

"longest-streak@npm:^3.0.0":
  version: 3.1.0
  resolution: "longest-streak@npm:3.1.0"
  checksum: 10c0/7c2f02d0454b52834d1bcedef79c557bd295ee71fdabb02d041ff3aa9da48a90b5df7c0409156dedbc4df9b65da18742652aaea4759d6ece01f08971af6a7eaa
  languageName: node
  linkType: hard

"loose-envify@npm:^1.1.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 10c0/655d110220983c1a4b9c0c679a2e8016d4b67f6e9c7b5435ff5979ecdb20d0813f4dec0a08674fcbdd4846a3f07edbb50a36811fd37930b94aaa0d9daceb017e
  languageName: node
  linkType: hard

"lru-cache@npm:^4.0.1":
  version: 4.1.5
  resolution: "lru-cache@npm:4.1.5"
  dependencies:
    pseudomap: "npm:^1.0.2"
    yallist: "npm:^2.1.2"
  checksum: 10c0/1ca5306814e5add9ec63556d6fd9b24a4ecdeaef8e9cea52cbf30301e6b88c8d8ddc7cab45b59b56eb763e6c45af911585dc89925a074ab65e1502e3fe8103cf
  languageName: node
  linkType: hard

"markdown-extensions@npm:^1.0.0":
  version: 1.1.1
  resolution: "markdown-extensions@npm:1.1.1"
  checksum: 10c0/eb9154016502ad1fb4477683ddb5cae8ba3ca06451b381b04dc4c34e91d8d168129d50d404b717d6bf7d458e13088c109303fc72d57cee7151a6082b0e7bba71
  languageName: node
  linkType: hard

"markdown-table@npm:^3.0.0":
  version: 3.0.4
  resolution: "markdown-table@npm:3.0.4"
  checksum: 10c0/1257b31827629a54c24a5030a3dac952256c559174c95ce3ef89bebd6bff0cb1444b1fd667b1a1bb53307f83278111505b3e26f0c4e7b731e0060d435d2d930b
  languageName: node
  linkType: hard

"mdast-util-definitions@npm:^5.0.0":
  version: 5.1.2
  resolution: "mdast-util-definitions@npm:5.1.2"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    "@types/unist": "npm:^2.0.0"
    unist-util-visit: "npm:^4.0.0"
  checksum: 10c0/da9049c15562e44ee4ea4a36113d98c6c9eaa3d8a17d6da2aef6a0626376dcd01d9ec007d77a8dfcad6d0cbd5c32a4abbad72a3f48c3172a55934c7d9a916480
  languageName: node
  linkType: hard

"mdast-util-find-and-replace@npm:^2.0.0":
  version: 2.2.2
  resolution: "mdast-util-find-and-replace@npm:2.2.2"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    escape-string-regexp: "npm:^5.0.0"
    unist-util-is: "npm:^5.0.0"
    unist-util-visit-parents: "npm:^5.0.0"
  checksum: 10c0/ce935f4bd4aeab47f91531a7f09dfab89aaeea62ad31029b43185c5b626921357703d8e5093c13073c097fdabfc57cb2f884d7dfad83dbe7239e351375d6797c
  languageName: node
  linkType: hard

"mdast-util-from-markdown@npm:^1.0.0, mdast-util-from-markdown@npm:^1.1.0, mdast-util-from-markdown@npm:^1.3.0":
  version: 1.3.1
  resolution: "mdast-util-from-markdown@npm:1.3.1"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    "@types/unist": "npm:^2.0.0"
    decode-named-character-reference: "npm:^1.0.0"
    mdast-util-to-string: "npm:^3.1.0"
    micromark: "npm:^3.0.0"
    micromark-util-decode-numeric-character-reference: "npm:^1.0.0"
    micromark-util-decode-string: "npm:^1.0.0"
    micromark-util-normalize-identifier: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    unist-util-stringify-position: "npm:^3.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/f4e901bf2a2e93fe35a339e0cff581efacce2f7117cd5652e9a270847bd7e2508b3e717b7b4156af54d4f896d63033e06ff9fafbf59a1d46fe17dd5e2a3f7846
  languageName: node
  linkType: hard

"mdast-util-gfm-autolink-literal@npm:^1.0.0":
  version: 1.0.3
  resolution: "mdast-util-gfm-autolink-literal@npm:1.0.3"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    ccount: "npm:^2.0.0"
    mdast-util-find-and-replace: "npm:^2.0.0"
    micromark-util-character: "npm:^1.0.0"
  checksum: 10c0/750e312eae73c3f2e8aa0e8c5232cb1b905357ff37ac236927f1af50cdbee7c2cfe2379b148ac32fa4137eeb3b24601e1bb6135084af926c7cd808867804193f
  languageName: node
  linkType: hard

"mdast-util-gfm-footnote@npm:^1.0.0":
  version: 1.0.2
  resolution: "mdast-util-gfm-footnote@npm:1.0.2"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-to-markdown: "npm:^1.3.0"
    micromark-util-normalize-identifier: "npm:^1.0.0"
  checksum: 10c0/767973e46b9e2ae44e80e51a5e38ad0b032fc7f06a1a3095aa96c2886ba333941c764474a56b82e7db05efc56242a4789bc7fbbcc753d61512750e86a4192fe8
  languageName: node
  linkType: hard

"mdast-util-gfm-strikethrough@npm:^1.0.0":
  version: 1.0.3
  resolution: "mdast-util-gfm-strikethrough@npm:1.0.3"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-to-markdown: "npm:^1.3.0"
  checksum: 10c0/29616b3dfdd33d3cd13f9b3181a8562fa2fbacfcb04a37dba3c690ba6829f0231b145444de984726d9277b2bc90dd7d96fb9df9f6292d5e77d65a8659ee2f52b
  languageName: node
  linkType: hard

"mdast-util-gfm-table@npm:^1.0.0":
  version: 1.0.7
  resolution: "mdast-util-gfm-table@npm:1.0.7"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    markdown-table: "npm:^3.0.0"
    mdast-util-from-markdown: "npm:^1.0.0"
    mdast-util-to-markdown: "npm:^1.3.0"
  checksum: 10c0/a37a05a936292c4f48394123332d3c034a6e1b15bb3e7f3b94e6bce3260c9184fd388abbc4100827edd5485a6563098306994d15a729bde3c96de7a62ed5720b
  languageName: node
  linkType: hard

"mdast-util-gfm-task-list-item@npm:^1.0.0":
  version: 1.0.2
  resolution: "mdast-util-gfm-task-list-item@npm:1.0.2"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-to-markdown: "npm:^1.3.0"
  checksum: 10c0/91fa91f7d1a8797bf129008dab12d23917015ad12df00044e275b4459e8b383fbec6234338953a0089ef9c3a114d0a360c3e652eb0ebf6ece7e7a8fd3b5977c6
  languageName: node
  linkType: hard

"mdast-util-gfm@npm:^2.0.0":
  version: 2.0.2
  resolution: "mdast-util-gfm@npm:2.0.2"
  dependencies:
    mdast-util-from-markdown: "npm:^1.0.0"
    mdast-util-gfm-autolink-literal: "npm:^1.0.0"
    mdast-util-gfm-footnote: "npm:^1.0.0"
    mdast-util-gfm-strikethrough: "npm:^1.0.0"
    mdast-util-gfm-table: "npm:^1.0.0"
    mdast-util-gfm-task-list-item: "npm:^1.0.0"
    mdast-util-to-markdown: "npm:^1.0.0"
  checksum: 10c0/5b7f7f98a90a2962d7e0787e080c4e55b70119100c7685bbdb772d8d7865524aeffd1757edba5afba434250e0246b987c0617c2c635baaf51c26dbbb3b72dbec
  languageName: node
  linkType: hard

"mdast-util-math@npm:^2.0.0":
  version: 2.0.2
  resolution: "mdast-util-math@npm:2.0.2"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    longest-streak: "npm:^3.0.0"
    mdast-util-to-markdown: "npm:^1.3.0"
  checksum: 10c0/2270b6f8d7f0eb7dd5c27bee8ad43f29a8e76a7092742945fd115480ddd8bf72ae53ba1f8f63697cec82016e0c169f0a201503862dfe6bc7ac2286662de3fe8e
  languageName: node
  linkType: hard

"mdast-util-mdx-expression@npm:^1.0.0":
  version: 1.3.2
  resolution: "mdast-util-mdx-expression@npm:1.3.2"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/hast": "npm:^2.0.0"
    "@types/mdast": "npm:^3.0.0"
    mdast-util-from-markdown: "npm:^1.0.0"
    mdast-util-to-markdown: "npm:^1.0.0"
  checksum: 10c0/01f306ee809d28825cbec23b3c80376a0fbe69601b6b2843d23beb5662a31ec7560995f52b96b13093cc03de1130404a47f139d16f58c3f54e91e88f4bdd82d2
  languageName: node
  linkType: hard

"mdast-util-mdx-jsx@npm:^2.0.0":
  version: 2.1.4
  resolution: "mdast-util-mdx-jsx@npm:2.1.4"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/hast": "npm:^2.0.0"
    "@types/mdast": "npm:^3.0.0"
    "@types/unist": "npm:^2.0.0"
    ccount: "npm:^2.0.0"
    mdast-util-from-markdown: "npm:^1.1.0"
    mdast-util-to-markdown: "npm:^1.3.0"
    parse-entities: "npm:^4.0.0"
    stringify-entities: "npm:^4.0.0"
    unist-util-remove-position: "npm:^4.0.0"
    unist-util-stringify-position: "npm:^3.0.0"
    vfile-message: "npm:^3.0.0"
  checksum: 10c0/b0c16e56a99c5167e60c98dbdbe82645549630fb529688642c4664ca5557ff0b3029c75146f5657cadb7908d5fa99810eacc5dcc51676d0877c8b4dcebb11cbe
  languageName: node
  linkType: hard

"mdast-util-mdx@npm:^2.0.0":
  version: 2.0.1
  resolution: "mdast-util-mdx@npm:2.0.1"
  dependencies:
    mdast-util-from-markdown: "npm:^1.0.0"
    mdast-util-mdx-expression: "npm:^1.0.0"
    mdast-util-mdx-jsx: "npm:^2.0.0"
    mdast-util-mdxjs-esm: "npm:^1.0.0"
    mdast-util-to-markdown: "npm:^1.0.0"
  checksum: 10c0/3b5e55781a7b7b4b7e71728a84afbec63516f251b3556efec52dbb4824c0733f5ebaa907d21211d008e5cb1a8265e6704bc062ee605f4c09e90fbfa2c6fbba3b
  languageName: node
  linkType: hard

"mdast-util-mdxjs-esm@npm:^1.0.0":
  version: 1.3.1
  resolution: "mdast-util-mdxjs-esm@npm:1.3.1"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/hast": "npm:^2.0.0"
    "@types/mdast": "npm:^3.0.0"
    mdast-util-from-markdown: "npm:^1.0.0"
    mdast-util-to-markdown: "npm:^1.0.0"
  checksum: 10c0/2ff0af34ea62004d39f15bd45b79e3008e68cae7e2510c9281e24a17e2c3f55d004524796166ef5aa3378798ca7f6c5f88883238f413577619bbaf41026b7e62
  languageName: node
  linkType: hard

"mdast-util-phrasing@npm:^3.0.0":
  version: 3.0.1
  resolution: "mdast-util-phrasing@npm:3.0.1"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    unist-util-is: "npm:^5.0.0"
  checksum: 10c0/5e00e303652a7581593549dbce20dfb69d687d79a972f7928f6ca1920ef5385bceb737a3d5292ab6d937ed8c67bb59771e80e88f530b78734fe7d155f833e32b
  languageName: node
  linkType: hard

"mdast-util-to-hast@npm:^12.1.0":
  version: 12.3.0
  resolution: "mdast-util-to-hast@npm:12.3.0"
  dependencies:
    "@types/hast": "npm:^2.0.0"
    "@types/mdast": "npm:^3.0.0"
    mdast-util-definitions: "npm:^5.0.0"
    micromark-util-sanitize-uri: "npm:^1.1.0"
    trim-lines: "npm:^3.0.0"
    unist-util-generated: "npm:^2.0.0"
    unist-util-position: "npm:^4.0.0"
    unist-util-visit: "npm:^4.0.0"
  checksum: 10c0/0753e45bfcce423f7a13979ac720a23ed8d6bafed174c387f43bbe8baf3838f3a043cd8006975b71e5c4068b7948f83f1348acea79801101af31eaec4e7a499a
  languageName: node
  linkType: hard

"mdast-util-to-hast@npm:^13.0.0":
  version: 13.2.0
  resolution: "mdast-util-to-hast@npm:13.2.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/mdast": "npm:^4.0.0"
    "@ungap/structured-clone": "npm:^1.0.0"
    devlop: "npm:^1.0.0"
    micromark-util-sanitize-uri: "npm:^2.0.0"
    trim-lines: "npm:^3.0.0"
    unist-util-position: "npm:^5.0.0"
    unist-util-visit: "npm:^5.0.0"
    vfile: "npm:^6.0.0"
  checksum: 10c0/9ee58def9287df8350cbb6f83ced90f9c088d72d4153780ad37854f87144cadc6f27b20347073b285173b1649b0723ddf0b9c78158608a804dcacb6bda6e1816
  languageName: node
  linkType: hard

"mdast-util-to-markdown@npm:^1.0.0, mdast-util-to-markdown@npm:^1.3.0":
  version: 1.5.0
  resolution: "mdast-util-to-markdown@npm:1.5.0"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    "@types/unist": "npm:^2.0.0"
    longest-streak: "npm:^3.0.0"
    mdast-util-phrasing: "npm:^3.0.0"
    mdast-util-to-string: "npm:^3.0.0"
    micromark-util-decode-string: "npm:^1.0.0"
    unist-util-visit: "npm:^4.0.0"
    zwitch: "npm:^2.0.0"
  checksum: 10c0/9831d14aa6c097750a90c7b87b4e814b040731c30606a794c9b136dc746633dd9ec07154ca97d4fec4eaf732cf89d14643424e2581732d6ee18c9b0e51ff7664
  languageName: node
  linkType: hard

"mdast-util-to-string@npm:^3.0.0, mdast-util-to-string@npm:^3.1.0":
  version: 3.2.0
  resolution: "mdast-util-to-string@npm:3.2.0"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
  checksum: 10c0/112f4bf0f6758dcb95deffdcf37afba7eaecdfe2ee13252de031723094d4d55220e147326690a8b91244758e2d678e7aeb1fdd0fa6ef3317c979bc42effd9a21
  languageName: node
  linkType: hard

"mermaid@npm:^10.2.2":
  version: 10.9.3
  resolution: "mermaid@npm:10.9.3"
  dependencies:
    "@braintree/sanitize-url": "npm:^6.0.1"
    "@types/d3-scale": "npm:^4.0.3"
    "@types/d3-scale-chromatic": "npm:^3.0.0"
    cytoscape: "npm:^3.28.1"
    cytoscape-cose-bilkent: "npm:^4.1.0"
    d3: "npm:^7.4.0"
    d3-sankey: "npm:^0.12.3"
    dagre-d3-es: "npm:7.0.10"
    dayjs: "npm:^1.11.7"
    dompurify: "npm:^3.0.5 <3.1.7"
    elkjs: "npm:^0.9.0"
    katex: "npm:^0.16.9"
    khroma: "npm:^2.0.0"
    lodash-es: "npm:^4.17.21"
    mdast-util-from-markdown: "npm:^1.3.0"
    non-layered-tidy-tree-layout: "npm:^2.0.2"
    stylis: "npm:^4.1.3"
    ts-dedent: "npm:^2.2.0"
    uuid: "npm:^9.0.0"
    web-worker: "npm:^1.2.0"
  checksum: 10c0/5f60222b34f4d27f18575f95a428e80135f0bbb3c1aade8de65e8d6c30a4f28a22f0547fcdb01d9bfcad95715631e8dc84017b7dd297bbb292ff3465dc887a16
  languageName: node
  linkType: hard

"micromark-core-commonmark@npm:^1.0.0, micromark-core-commonmark@npm:^1.0.1":
  version: 1.1.0
  resolution: "micromark-core-commonmark@npm:1.1.0"
  dependencies:
    decode-named-character-reference: "npm:^1.0.0"
    micromark-factory-destination: "npm:^1.0.0"
    micromark-factory-label: "npm:^1.0.0"
    micromark-factory-space: "npm:^1.0.0"
    micromark-factory-title: "npm:^1.0.0"
    micromark-factory-whitespace: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-chunked: "npm:^1.0.0"
    micromark-util-classify-character: "npm:^1.0.0"
    micromark-util-html-tag-name: "npm:^1.0.0"
    micromark-util-normalize-identifier: "npm:^1.0.0"
    micromark-util-resolve-all: "npm:^1.0.0"
    micromark-util-subtokenize: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.1"
    uvu: "npm:^0.5.0"
  checksum: 10c0/b3bf7b7004ce7dbb3ae151dcca4db1d12546f1b943affb2418da4b90b9ce59357373c433ee2eea4c868aee0791dafa355aeed19f5ef2b0acaf271f32f1ecbe6a
  languageName: node
  linkType: hard

"micromark-extension-gfm-autolink-literal@npm:^1.0.0":
  version: 1.0.5
  resolution: "micromark-extension-gfm-autolink-literal@npm:1.0.5"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-sanitize-uri: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/4964a52605ac36d24501d427e2d173fa39b5e0402275cb45068eba4898f4cb9cc57f7007b21b7514f0ab5f7b371b1701a5156a10b6ac8e77a7f36e830cf481d4
  languageName: node
  linkType: hard

"micromark-extension-gfm-footnote@npm:^1.0.0":
  version: 1.1.2
  resolution: "micromark-extension-gfm-footnote@npm:1.1.2"
  dependencies:
    micromark-core-commonmark: "npm:^1.0.0"
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-normalize-identifier: "npm:^1.0.0"
    micromark-util-sanitize-uri: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/b8090876cc3da5436c6253b0b40e39ceaa470c2429f699c19ee4163cef3102c4cd16c4ac2ec8caf916037fad310cfb52a9ef182c75d50fca7419ba08faad9b39
  languageName: node
  linkType: hard

"micromark-extension-gfm-strikethrough@npm:^1.0.0":
  version: 1.0.7
  resolution: "micromark-extension-gfm-strikethrough@npm:1.0.7"
  dependencies:
    micromark-util-chunked: "npm:^1.0.0"
    micromark-util-classify-character: "npm:^1.0.0"
    micromark-util-resolve-all: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/b45fe93a7a412fc44bae7a183b92a988e17b49ed9d683bd80ee4dde96d462e1ca6b316dd64bda7759e4086d6d8686790a711e53c244f1f4d2b37e1cfe852884d
  languageName: node
  linkType: hard

"micromark-extension-gfm-table@npm:^1.0.0":
  version: 1.0.7
  resolution: "micromark-extension-gfm-table@npm:1.0.7"
  dependencies:
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/38b5af80ecab8206845a057338235bee6f47fb6cb904208be4b76e87906765821683e25bef85dfa485809f931eaf8cd55f16cd2f4d6e33b84f56edfaf1dfb129
  languageName: node
  linkType: hard

"micromark-extension-gfm-tagfilter@npm:^1.0.0":
  version: 1.0.2
  resolution: "micromark-extension-gfm-tagfilter@npm:1.0.2"
  dependencies:
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/7e1bf278255cf2a8d2dda9de84bc238b39c53100e25ba8d7168220d5b00dc74869a6cb038fbf2e76b8ae89efc66906762311797a906d7d9cdd71e07bfe1ed505
  languageName: node
  linkType: hard

"micromark-extension-gfm-task-list-item@npm:^1.0.0":
  version: 1.0.5
  resolution: "micromark-extension-gfm-task-list-item@npm:1.0.5"
  dependencies:
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/2179742fa2cbb243cc06bd9e43fbb94cd98e4814c9d368ddf8b4b5afa0348023f335626ae955e89d679e2c2662a7f82c315117a3b060c87bdb4420fee5a219d1
  languageName: node
  linkType: hard

"micromark-extension-gfm@npm:^2.0.0":
  version: 2.0.3
  resolution: "micromark-extension-gfm@npm:2.0.3"
  dependencies:
    micromark-extension-gfm-autolink-literal: "npm:^1.0.0"
    micromark-extension-gfm-footnote: "npm:^1.0.0"
    micromark-extension-gfm-strikethrough: "npm:^1.0.0"
    micromark-extension-gfm-table: "npm:^1.0.0"
    micromark-extension-gfm-tagfilter: "npm:^1.0.0"
    micromark-extension-gfm-task-list-item: "npm:^1.0.0"
    micromark-util-combine-extensions: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/53056376d14caf3fab2cc44881c1ad49d975776cc2267bca74abda2cb31f2a77ec0fb2bdb2dd97565f0d9943ad915ff192b89c1cee5d9d727569a5e38505799b
  languageName: node
  linkType: hard

"micromark-extension-math@npm:^2.0.0":
  version: 2.1.2
  resolution: "micromark-extension-math@npm:2.1.2"
  dependencies:
    "@types/katex": "npm:^0.16.0"
    katex: "npm:^0.16.0"
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/5d40ffc93862498cbcbc9c96a40a05150b878c3d86ab25bc771dec005d286f4381578ccee3f421ecfd9db259298a89a37a5b6b48529842240d34f8acd8edffb5
  languageName: node
  linkType: hard

"micromark-extension-mdx-expression@npm:^1.0.0":
  version: 1.0.8
  resolution: "micromark-extension-mdx-expression@npm:1.0.8"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    micromark-factory-mdx-expression: "npm:^1.0.0"
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-events-to-acorn: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/99e2997a54caafc4258979c0591b3fe8e31018079df833d559768092fec41e57a71225d423f4179cea4e8bc1af2f52f5c9ae640673619d8fe142ded875240da3
  languageName: node
  linkType: hard

"micromark-extension-mdx-jsx@npm:^1.0.0":
  version: 1.0.5
  resolution: "micromark-extension-mdx-jsx@npm:1.0.5"
  dependencies:
    "@types/acorn": "npm:^4.0.0"
    "@types/estree": "npm:^1.0.0"
    estree-util-is-identifier-name: "npm:^2.0.0"
    micromark-factory-mdx-expression: "npm:^1.0.0"
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
    vfile-message: "npm:^3.0.0"
  checksum: 10c0/1b4bfbe60b9cabfabfb870f70ded8da0caacbaa3be6bdf07f6db25cc5a14c6bc970c34c60e5c80da1e97766064a117feb8160b6d661d69e530a4cc7ec97305de
  languageName: node
  linkType: hard

"micromark-extension-mdx-md@npm:^1.0.0":
  version: 1.0.1
  resolution: "micromark-extension-mdx-md@npm:1.0.1"
  dependencies:
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/9ad70b3a5e842fd7ebd93c8c48a32fd3d05fe77be06a08ef32462ea53e97d8f297e2c1c4b30a6929dbd05125279fe98bb04e9cc0bb686c691bdcf7d36c6e51b0
  languageName: node
  linkType: hard

"micromark-extension-mdxjs-esm@npm:^1.0.0":
  version: 1.0.5
  resolution: "micromark-extension-mdxjs-esm@npm:1.0.5"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    micromark-core-commonmark: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-events-to-acorn: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    unist-util-position-from-estree: "npm:^1.1.0"
    uvu: "npm:^0.5.0"
    vfile-message: "npm:^3.0.0"
  checksum: 10c0/612028bced78e882641a43c78fc4813a573b383dc0a7b90db75ed88b37bf5b5997dc7ead4a1011315b34f17bc76b7f4419de6ad9532a088102ab1eea0245d380
  languageName: node
  linkType: hard

"micromark-extension-mdxjs@npm:^1.0.0":
  version: 1.0.1
  resolution: "micromark-extension-mdxjs@npm:1.0.1"
  dependencies:
    acorn: "npm:^8.0.0"
    acorn-jsx: "npm:^5.0.0"
    micromark-extension-mdx-expression: "npm:^1.0.0"
    micromark-extension-mdx-jsx: "npm:^1.0.0"
    micromark-extension-mdx-md: "npm:^1.0.0"
    micromark-extension-mdxjs-esm: "npm:^1.0.0"
    micromark-util-combine-extensions: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/3f123e4afea9674c96934c9ea6a057ec9e5584992c50c36c173a2e331d272b1f4e2a8552364a0e2cb50703d0218831fdae1a17b563f0009aac6a35350e6a7b77
  languageName: node
  linkType: hard

"micromark-factory-destination@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-destination@npm:1.1.0"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/71ebd9089bf0c9689b98ef42215c04032ae2701ae08c3546b663628553255dca18e5310dbdacddad3acd8de4f12a789835fff30dadc4da3c4e30387a75e6b488
  languageName: node
  linkType: hard

"micromark-factory-label@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-label@npm:1.1.0"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/5e2cd2d8214bb92a34dfcedf9c7aecf565e3648650a3a6a0495ededf15f2318dd214dc069e3026402792cd5839d395313f8ef9c2e86ca34a8facaa0f75a77753
  languageName: node
  linkType: hard

"micromark-factory-mdx-expression@npm:^1.0.0":
  version: 1.0.9
  resolution: "micromark-factory-mdx-expression@npm:1.0.9"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-events-to-acorn: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    unist-util-position-from-estree: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
    vfile-message: "npm:^3.0.0"
  checksum: 10c0/b28bd8e072f37ca91446fe8d113e4ae64baaef013b0cde4aa224add0ee40963ce3584b9709f7662d30491f875ae7104b897d37efa26cdaecf25082ed5bac7b8c
  languageName: node
  linkType: hard

"micromark-factory-space@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-space@npm:1.1.0"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/3da81187ce003dd4178c7adc4674052fb8befc8f1a700ae4c8227755f38581a4ae963866dc4857488d62d1dc9837606c9f2f435fa1332f62a0f1c49b83c6a822
  languageName: node
  linkType: hard

"micromark-factory-title@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-title@npm:1.1.0"
  dependencies:
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/cf8c687d1d5c3928846a4791d4a7e2f1d7bdd2397051e20d60f06b7565a48bf85198ab6f85735e997ab3f0cbb80b8b6391f4f7ebc0aae2f2f8c3a08541257bf6
  languageName: node
  linkType: hard

"micromark-factory-whitespace@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-whitespace@npm:1.1.0"
  dependencies:
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/7248cc4534f9befb38c6f398b6e38efd3199f1428fc214c9cb7ed5b6e9fa7a82c0d8cdfa9bcacde62887c9a7c8c46baf5c318b2ae8f701afbccc8ad702e92dce
  languageName: node
  linkType: hard

"micromark-util-character@npm:^1.0.0":
  version: 1.2.0
  resolution: "micromark-util-character@npm:1.2.0"
  dependencies:
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/3390a675a50731b58a8e5493cd802e190427f10fa782079b455b00f6b54e406e36882df7d4a3bd32b709f7a2c3735b4912597ebc1c0a99566a8d8d0b816e2cd4
  languageName: node
  linkType: hard

"micromark-util-character@npm:^2.0.0":
  version: 2.1.1
  resolution: "micromark-util-character@npm:2.1.1"
  dependencies:
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10c0/d3fe7a5e2c4060fc2a076f9ce699c82a2e87190a3946e1e5eea77f563869b504961f5668d9c9c014724db28ac32fa909070ea8b30c3a39bd0483cc6c04cc76a1
  languageName: node
  linkType: hard

"micromark-util-chunked@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-chunked@npm:1.1.0"
  dependencies:
    micromark-util-symbol: "npm:^1.0.0"
  checksum: 10c0/59534cf4aaf481ed58d65478d00eae0080df9b5816673f79b5ddb0cea263e5a9ee9cbb6cc565daf1eb3c8c4ff86fc4e25d38a0577539655cda823a4249efd358
  languageName: node
  linkType: hard

"micromark-util-classify-character@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-classify-character@npm:1.1.0"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/3266453dc0fdaf584e24c9b3c91d1ed180f76b5856699c51fd2549305814fcab7ec52afb4d3e83d002a9115cd2d2b2ffdc9c0b38ed85120822bf515cc00636ec
  languageName: node
  linkType: hard

"micromark-util-combine-extensions@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-combine-extensions@npm:1.1.0"
  dependencies:
    micromark-util-chunked: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/0bc572fab3fe77f533c29aa1b75cb847b9fc9455f67a98623ef9740b925c0b0426ad9f09bbb56f1e844ea9ebada7873d1f06d27f7c979a917692b273c4b69e31
  languageName: node
  linkType: hard

"micromark-util-decode-numeric-character-reference@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-decode-numeric-character-reference@npm:1.1.0"
  dependencies:
    micromark-util-symbol: "npm:^1.0.0"
  checksum: 10c0/64ef2575e3fc2426976c19e16973348f20b59ddd5543f1467ac2e251f29e0a91f12089703d29ae985b0b9a408ee0d72f06d04ed3920811aa2402aabca3bdf9e4
  languageName: node
  linkType: hard

"micromark-util-decode-string@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-decode-string@npm:1.1.0"
  dependencies:
    decode-named-character-reference: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-decode-numeric-character-reference: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
  checksum: 10c0/757a0aaa5ad6c50c7480bd75371d407ac75f5022cd4404aba07adadf1448189502aea9bb7b2d09d25e18745e0abf72b95506b6beb184bcccabe919e48e3a5df7
  languageName: node
  linkType: hard

"micromark-util-encode@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-encode@npm:1.1.0"
  checksum: 10c0/9878c9bc96999d45626a7597fffac85348ea842dce75d2417345cbf070a9941c62477bd0963bef37d4f0fd29f2982be6ddf416d62806f00ccb334af9d6ee87e7
  languageName: node
  linkType: hard

"micromark-util-encode@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-encode@npm:2.0.1"
  checksum: 10c0/b2b29f901093845da8a1bf997ea8b7f5e061ffdba85070dfe14b0197c48fda64ffcf82bfe53c90cf9dc185e69eef8c5d41cae3ba918b96bc279326921b59008a
  languageName: node
  linkType: hard

"micromark-util-events-to-acorn@npm:^1.0.0":
  version: 1.2.3
  resolution: "micromark-util-events-to-acorn@npm:1.2.3"
  dependencies:
    "@types/acorn": "npm:^4.0.0"
    "@types/estree": "npm:^1.0.0"
    "@types/unist": "npm:^2.0.0"
    estree-util-visit: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
    vfile-message: "npm:^3.0.0"
  checksum: 10c0/cd3af7365806a0b22efb83cb7726cb835725c0bc22e04f7ea83f2f38a09e7132413eff6ab6d53652b969a7ec30e442731c3abbbe8a74dc2081c51fd10223c269
  languageName: node
  linkType: hard

"micromark-util-html-tag-name@npm:^1.0.0":
  version: 1.2.0
  resolution: "micromark-util-html-tag-name@npm:1.2.0"
  checksum: 10c0/15421869678d36b4fe51df453921e8186bff514a14e9f79f32b7e1cdd67874e22a66ad34a7f048dd132cbbbfc7c382ae2f777a2bfd1f245a47705dc1c6d4f199
  languageName: node
  linkType: hard

"micromark-util-normalize-identifier@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-normalize-identifier@npm:1.1.0"
  dependencies:
    micromark-util-symbol: "npm:^1.0.0"
  checksum: 10c0/a9657321a2392584e4d978061882117a84db7d2c2c1c052c0f5d25da089d463edb9f956d5beaf7f5768984b6f72d046d59b5972951ec7bf25397687a62b8278a
  languageName: node
  linkType: hard

"micromark-util-resolve-all@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-resolve-all@npm:1.1.0"
  dependencies:
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/b5c95484c06e87bbbb60d8430eb030a458733a5270409f4c67892d1274737087ca6a7ca888987430e57cf1dcd44bb16390d3b3936a2bf07f7534ec8f52ce43c9
  languageName: node
  linkType: hard

"micromark-util-sanitize-uri@npm:^1.0.0, micromark-util-sanitize-uri@npm:^1.1.0":
  version: 1.2.0
  resolution: "micromark-util-sanitize-uri@npm:1.2.0"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-encode: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
  checksum: 10c0/dbdb98248e9f0408c7a00f1c1cd805775b41d213defd659533835f34b38da38e8f990bf7b3f782e96bffbc549aec9c3ecdab197d4ad5adbfe08f814a70327b6e
  languageName: node
  linkType: hard

"micromark-util-sanitize-uri@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-sanitize-uri@npm:2.0.1"
  dependencies:
    micromark-util-character: "npm:^2.0.0"
    micromark-util-encode: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
  checksum: 10c0/60e92166e1870fd4f1961468c2651013ff760617342918e0e0c3c4e872433aa2e60c1e5a672bfe5d89dc98f742d6b33897585cf86ae002cda23e905a3c02527c
  languageName: node
  linkType: hard

"micromark-util-subtokenize@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-subtokenize@npm:1.1.0"
  dependencies:
    micromark-util-chunked: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/f292b1b162845db50d36255c9d4c4c6d47931fbca3ac98a80c7e536d2163233fd662f8ca0479ee2b80f145c66a1394c7ed17dfce801439741211015e77e3901e
  languageName: node
  linkType: hard

"micromark-util-symbol@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-symbol@npm:1.1.0"
  checksum: 10c0/10ceaed33a90e6bfd3a5d57053dbb53f437d4809cc11430b5a09479c0ba601577059be9286df4a7eae6e350a60a2575dc9fa9d9872b5b8d058c875e075c33803
  languageName: node
  linkType: hard

"micromark-util-symbol@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-symbol@npm:2.0.1"
  checksum: 10c0/f2d1b207771e573232436618e78c5e46cd4b5c560dd4a6d63863d58018abbf49cb96ec69f7007471e51434c60de3c9268ef2bf46852f26ff4aacd10f9da16fe9
  languageName: node
  linkType: hard

"micromark-util-types@npm:^1.0.0, micromark-util-types@npm:^1.0.1":
  version: 1.1.0
  resolution: "micromark-util-types@npm:1.1.0"
  checksum: 10c0/a9749cb0a12a252ff536baabcb7012421b6fad4d91a5fdd80d7b33dc7b4c22e2d0c4637dfe5b902d00247fe6c9b01f4a24fce6b572b16ccaa4da90e6ce2a11e4
  languageName: node
  linkType: hard

"micromark-util-types@npm:^2.0.0":
  version: 2.0.2
  resolution: "micromark-util-types@npm:2.0.2"
  checksum: 10c0/c8c15b96c858db781c4393f55feec10004bf7df95487636c9a9f7209e51002a5cca6a047c5d2a5dc669ff92da20e57aaa881e81a268d9ccadb647f9dce305298
  languageName: node
  linkType: hard

"micromark@npm:^3.0.0":
  version: 3.2.0
  resolution: "micromark@npm:3.2.0"
  dependencies:
    "@types/debug": "npm:^4.0.0"
    debug: "npm:^4.0.0"
    decode-named-character-reference: "npm:^1.0.0"
    micromark-core-commonmark: "npm:^1.0.1"
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-chunked: "npm:^1.0.0"
    micromark-util-combine-extensions: "npm:^1.0.0"
    micromark-util-decode-numeric-character-reference: "npm:^1.0.0"
    micromark-util-encode: "npm:^1.0.0"
    micromark-util-normalize-identifier: "npm:^1.0.0"
    micromark-util-resolve-all: "npm:^1.0.0"
    micromark-util-sanitize-uri: "npm:^1.0.0"
    micromark-util-subtokenize: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.1"
    uvu: "npm:^0.5.0"
  checksum: 10c0/f243e805d1b3cc699fddae2de0b1492bc82462f1a709d7ae5c82039f88b1e009c959100184717e748be057b5f88603289d5681679a4e6fbabcd037beb34bc744
  languageName: node
  linkType: hard

"mime-db@npm:~1.25.0":
  version: 1.25.0
  resolution: "mime-db@npm:1.25.0"
  checksum: 10c0/a90951c80700314d6131e106cbf56ae94718d046e9669a5afd7f91e603248da328aad5b63314865fd9547f4688465ac62d1ace59e1ffdb73688452a28a9c3622
  languageName: node
  linkType: hard

"mime-types@npm:2.1.13":
  version: 2.1.13
  resolution: "mime-types@npm:2.1.13"
  dependencies:
    mime-db: "npm:~1.25.0"
  checksum: 10c0/a53a0453f7863fa2d823025ce92978f5142a18528316caa53cbb1d4212be847973e9a5f2466362cbd2351a512e369252fc2c88c9c5bbc85df4bd8b42214f1785
  languageName: node
  linkType: hard

"mri@npm:^1.1.0":
  version: 1.2.0
  resolution: "mri@npm:1.2.0"
  checksum: 10c0/a3d32379c2554cf7351db6237ddc18dc9e54e4214953f3da105b97dc3babe0deb3ffe99cf409b38ea47cc29f9430561ba6b53b24ab8f9ce97a4b50409e4a50e7
  languageName: node
  linkType: hard

"ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.6":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10c0/40e7f70b3d15f725ca072dfc4f74e81fcf1fbb02e491cf58ac0c79093adc9b0a73b152bcde57df4b79cd097e13023d7504acb38404a4da7bc1cd8e887b82fe0b
  languageName: node
  linkType: hard

"next-mdx-remote@npm:^4.2.1":
  version: 4.4.1
  resolution: "next-mdx-remote@npm:4.4.1"
  dependencies:
    "@mdx-js/mdx": "npm:^2.2.1"
    "@mdx-js/react": "npm:^2.2.1"
    vfile: "npm:^5.3.0"
    vfile-matter: "npm:^3.0.1"
  peerDependencies:
    react: ">=16.x <=18.x"
    react-dom: ">=16.x <=18.x"
  checksum: 10c0/d48ad271f58312d11f392b0fbd7b2dbc5990cc82fcb6d28f687875a52b28b695c0700b93f197c72910a4c73da0a1fe4867db95315bc2ee7f0fc1743279f41b80
  languageName: node
  linkType: hard

"next-themes@npm:^0.2.1":
  version: 0.2.1
  resolution: "next-themes@npm:0.2.1"
  peerDependencies:
    next: "*"
    react: "*"
    react-dom: "*"
  checksum: 10c0/979dec0a2de049ce7d1b5da835e7f7dc3b7ec83ba9e464348f497a52a6a6e5b5c395c97f071f66a63f50f22cce89fb6d19061ec7e75643b0eab215b21794bde7
  languageName: node
  linkType: hard

"next@npm:latest":
  version: 15.4.2
  resolution: "next@npm:15.4.2"
  dependencies:
    "@next/env": "npm:15.4.2"
    "@next/swc-darwin-arm64": "npm:15.4.2"
    "@next/swc-darwin-x64": "npm:15.4.2"
    "@next/swc-linux-arm64-gnu": "npm:15.4.2"
    "@next/swc-linux-arm64-musl": "npm:15.4.2"
    "@next/swc-linux-x64-gnu": "npm:15.4.2"
    "@next/swc-linux-x64-musl": "npm:15.4.2"
    "@next/swc-win32-arm64-msvc": "npm:15.4.2"
    "@next/swc-win32-x64-msvc": "npm:15.4.2"
    "@swc/helpers": "npm:0.5.15"
    caniuse-lite: "npm:^1.0.30001579"
    postcss: "npm:8.4.31"
    sharp: "npm:^0.34.3"
    styled-jsx: "npm:5.1.6"
  peerDependencies:
    "@opentelemetry/api": ^1.1.0
    "@playwright/test": ^1.51.1
    babel-plugin-react-compiler: "*"
    react: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
    react-dom: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
    sass: ^1.3.0
  dependenciesMeta:
    "@next/swc-darwin-arm64":
      optional: true
    "@next/swc-darwin-x64":
      optional: true
    "@next/swc-linux-arm64-gnu":
      optional: true
    "@next/swc-linux-arm64-musl":
      optional: true
    "@next/swc-linux-x64-gnu":
      optional: true
    "@next/swc-linux-x64-musl":
      optional: true
    "@next/swc-win32-arm64-msvc":
      optional: true
    "@next/swc-win32-x64-msvc":
      optional: true
    sharp:
      optional: true
  peerDependenciesMeta:
    "@opentelemetry/api":
      optional: true
    "@playwright/test":
      optional: true
    babel-plugin-react-compiler:
      optional: true
    sass:
      optional: true
  bin:
    next: dist/bin/next
  checksum: 10c0/eac6b27e480b45c0441b18d3c1cadf0373847e407e8b032e1d72f87bd22642fbc31b7a9ba99a7d3cbb3e37d1cadc4e5999b0c14844aa6d3e0be6f7291dfe96b0
  languageName: node
  linkType: hard

"nextra-theme-blog@npm:^2.0.0-beta.5":
  version: 2.13.4
  resolution: "nextra-theme-blog@npm:2.13.4"
  dependencies:
    next-themes: "npm:^0.2.1"
  peerDependencies:
    next: ">=9.5.3"
    nextra: 2.13.4
    react: ">=16.13.1"
    react-cusdis: ^2.1.3
    react-dom: ">=16.13.1"
  peerDependenciesMeta:
    react-cusdis:
      optional: true
  checksum: 10c0/72a325ec094404b3eb956a55fc3265b5dfbcc8bc2f324feaae495761084ecc600b8ee0f3fa18915c0a6bb95c0bf5d57b769e43c99bceca2c88e066170520454d
  languageName: node
  linkType: hard

"nextra@npm:^2.0.0-beta.5":
  version: 2.13.4
  resolution: "nextra@npm:2.13.4"
  dependencies:
    "@headlessui/react": "npm:^1.7.17"
    "@mdx-js/mdx": "npm:^2.3.0"
    "@mdx-js/react": "npm:^2.3.0"
    "@napi-rs/simple-git": "npm:^0.1.9"
    "@theguild/remark-mermaid": "npm:^0.0.5"
    "@theguild/remark-npm2yarn": "npm:^0.2.0"
    clsx: "npm:^2.0.0"
    github-slugger: "npm:^2.0.0"
    graceful-fs: "npm:^4.2.11"
    gray-matter: "npm:^4.0.3"
    katex: "npm:^0.16.9"
    lodash.get: "npm:^4.4.2"
    next-mdx-remote: "npm:^4.2.1"
    p-limit: "npm:^3.1.0"
    rehype-katex: "npm:^7.0.0"
    rehype-pretty-code: "npm:0.9.11"
    rehype-raw: "npm:^7.0.0"
    remark-gfm: "npm:^3.0.1"
    remark-math: "npm:^5.1.1"
    remark-reading-time: "npm:^2.0.1"
    shiki: "npm:^0.14.3"
    slash: "npm:^3.0.0"
    title: "npm:^3.5.3"
    unist-util-remove: "npm:^4.0.0"
    unist-util-visit: "npm:^5.0.0"
    zod: "npm:^3.22.3"
  peerDependencies:
    next: ">=9.5.3"
    react: ">=16.13.1"
    react-dom: ">=16.13.1"
  checksum: 10c0/68941552f83639ae818e27b1cfbfef4031362c95bb5c80188cabe29ccd700e0889e20d90cde621d79e151fdf02713b096cfaa42b9304946133b82c223d2e01e3
  languageName: node
  linkType: hard

"non-layered-tidy-tree-layout@npm:^2.0.2":
  version: 2.0.2
  resolution: "non-layered-tidy-tree-layout@npm:2.0.2"
  checksum: 10c0/73856e9959667193e733a7ef2b06a69421f4d9d7428a3982ce39763cd979a04eed0007f2afb3414afa3f6dc4dc6b5c850c2af9aa71a974475236a465093ec9c7
  languageName: node
  linkType: hard

"npm-run-path@npm:^2.0.0":
  version: 2.0.2
  resolution: "npm-run-path@npm:2.0.2"
  dependencies:
    path-key: "npm:^2.0.0"
  checksum: 10c0/95549a477886f48346568c97b08c4fda9cdbf7ce8a4fbc2213f36896d0d19249e32d68d7451bdcbca8041b5fba04a6b2c4a618beaf19849505c05b700740f1de
  languageName: node
  linkType: hard

"npm-to-yarn@npm:^2.1.0":
  version: 2.2.1
  resolution: "npm-to-yarn@npm:2.2.1"
  checksum: 10c0/65c696a3e595facad802b6b13c04e504806ea88fd4f87ab758f8042c19f65b4c4822815a47095df944b0809a95e574c27323c33cca5533f8454515eaa6e14fac
  languageName: node
  linkType: hard

"p-finally@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-finally@npm:1.0.0"
  checksum: 10c0/6b8552339a71fe7bd424d01d8451eea92d379a711fc62f6b2fe64cad8a472c7259a236c9a22b4733abca0b5666ad503cb497792a0478c5af31ded793d00937e7
  languageName: node
  linkType: hard

"p-limit@npm:^3.1.0":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10c0/9db675949dbdc9c3763c89e748d0ef8bdad0afbb24d49ceaf4c46c02c77d30db4e0652ed36d0a0a7a95154335fab810d95c86153105bb73b3a90448e2bb14e1a
  languageName: node
  linkType: hard

"parse-entities@npm:^4.0.0":
  version: 4.0.2
  resolution: "parse-entities@npm:4.0.2"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    character-entities-legacy: "npm:^3.0.0"
    character-reference-invalid: "npm:^2.0.0"
    decode-named-character-reference: "npm:^1.0.0"
    is-alphanumerical: "npm:^2.0.0"
    is-decimal: "npm:^2.0.0"
    is-hexadecimal: "npm:^2.0.0"
  checksum: 10c0/a13906b1151750b78ed83d386294066daf5fb559e08c5af9591b2d98cc209123103016a01df776f65f8219ad26652d6d6b210d0974d452049cddfc53a8916c34
  languageName: node
  linkType: hard

"parse-numeric-range@npm:^1.3.0":
  version: 1.3.0
  resolution: "parse-numeric-range@npm:1.3.0"
  checksum: 10c0/53465afaa92111e86697281b684aa4574427360889cc23a1c215488c06b72441febdbf09f47ab0bef9a0c701e059629f3eebd2fe6fb241a254ad7a7a642aebe8
  languageName: node
  linkType: hard

"parse5@npm:^7.0.0":
  version: 7.3.0
  resolution: "parse5@npm:7.3.0"
  dependencies:
    entities: "npm:^6.0.0"
  checksum: 10c0/7fd2e4e247e85241d6f2a464d0085eed599a26d7b0a5233790c49f53473232eb85350e8133344d9b3fd58b89339e7ad7270fe1f89d28abe50674ec97b87f80b5
  languageName: node
  linkType: hard

"path-key@npm:^2.0.0":
  version: 2.0.1
  resolution: "path-key@npm:2.0.1"
  checksum: 10c0/dd2044f029a8e58ac31d2bf34c34b93c3095c1481942960e84dd2faa95bbb71b9b762a106aead0646695330936414b31ca0bd862bf488a937ad17c8c5d73b32b
  languageName: node
  linkType: hard

"periscopic@npm:^3.0.0":
  version: 3.1.0
  resolution: "periscopic@npm:3.1.0"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    estree-walker: "npm:^3.0.0"
    is-reference: "npm:^3.0.0"
  checksum: 10c0/fb5ce7cd810c49254cdf1cd3892811e6dd1a1dfbdf5f10a0a33fb7141baac36443c4cad4f0e2b30abd4eac613f6ab845c2bc1b7ce66ae9694c7321e6ada5bd96
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10c0/e2e3e8170ab9d7c7421969adaa7e1b31434f789afb9b3f115f6b96d91945041ac3ceb02e9ec6fe6510ff036bcc0bf91e69a1772edc0b707e12b19c0f2d6bcf58
  languageName: node
  linkType: hard

"postcss@npm:8.4.31":
  version: 8.4.31
  resolution: "postcss@npm:8.4.31"
  dependencies:
    nanoid: "npm:^3.3.6"
    picocolors: "npm:^1.0.0"
    source-map-js: "npm:^1.0.2"
  checksum: 10c0/748b82e6e5fc34034dcf2ae88ea3d11fd09f69b6c50ecdd3b4a875cfc7cdca435c958b211e2cb52355422ab6fccb7d8f2f2923161d7a1b281029e4a913d59acf
  languageName: node
  linkType: hard

"property-information@npm:^6.0.0":
  version: 6.5.0
  resolution: "property-information@npm:6.5.0"
  checksum: 10c0/981e0f9cc2e5acdb414a6fd48a99dd0fd3a4079e7a91ab41cf97a8534cf43e0e0bc1ffada6602a1b3d047a33db8b5fc2ef46d863507eda712d5ceedac443f0ef
  languageName: node
  linkType: hard

"property-information@npm:^7.0.0":
  version: 7.1.0
  resolution: "property-information@npm:7.1.0"
  checksum: 10c0/e0fe22cff26103260ad0e82959229106563fa115a54c4d6c183f49d88054e489cc9f23452d3ad584179dc13a8b7b37411a5df873746b5e4086c865874bfa968e
  languageName: node
  linkType: hard

"pseudomap@npm:^1.0.2":
  version: 1.0.2
  resolution: "pseudomap@npm:1.0.2"
  checksum: 10c0/5a91ce114c64ed3a6a553aa7d2943868811377388bb31447f9d8028271bae9b05b340fe0b6961a64e45b9c72946aeb0a4ab635e8f7cb3715ffd0ff2beeb6a679
  languageName: node
  linkType: hard

"react-dom@npm:^18.2.0":
  version: 18.3.1
  resolution: "react-dom@npm:18.3.1"
  dependencies:
    loose-envify: "npm:^1.1.0"
    scheduler: "npm:^0.23.2"
  peerDependencies:
    react: ^18.3.1
  checksum: 10c0/a752496c1941f958f2e8ac56239172296fcddce1365ce45222d04a1947e0cc5547df3e8447f855a81d6d39f008d7c32eab43db3712077f09e3f67c4874973e85
  languageName: node
  linkType: hard

"react@npm:^18.2.0":
  version: 18.3.1
  resolution: "react@npm:18.3.1"
  dependencies:
    loose-envify: "npm:^1.1.0"
  checksum: 10c0/283e8c5efcf37802c9d1ce767f302dd569dd97a70d9bb8c7be79a789b9902451e0d16334b05d73299b20f048cbc3c7d288bbbde10b701fa194e2089c237dbea3
  languageName: node
  linkType: hard

"reading-time@npm:^1.3.0":
  version: 1.5.0
  resolution: "reading-time@npm:1.5.0"
  checksum: 10c0/0f730852fd4fb99e5f78c5b0cf36ab8c3fa15db96f87d9563843f6fd07a47864273ade539ebb184b785b728cde81a70283aa2d9b80cba5ca03b81868be03cabc
  languageName: node
  linkType: hard

"rehype-katex@npm:^7.0.0":
  version: 7.0.1
  resolution: "rehype-katex@npm:7.0.1"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/katex": "npm:^0.16.0"
    hast-util-from-html-isomorphic: "npm:^2.0.0"
    hast-util-to-text: "npm:^4.0.0"
    katex: "npm:^0.16.0"
    unist-util-visit-parents: "npm:^6.0.0"
    vfile: "npm:^6.0.0"
  checksum: 10c0/73c770319536128b75055d904d06951789d00a0552c11724c0dac2e244dcb21041630552d118a11cc42233fdcd1bfee525e78a0020fde635bd916cceb281dfb1
  languageName: node
  linkType: hard

"rehype-pretty-code@npm:0.9.11":
  version: 0.9.11
  resolution: "rehype-pretty-code@npm:0.9.11"
  dependencies:
    "@types/hast": "npm:^2.0.0"
    hash-obj: "npm:^4.0.0"
    parse-numeric-range: "npm:^1.3.0"
  peerDependencies:
    shiki: "*"
  checksum: 10c0/10d9b87df6b9a963f6e650b90908347e6cce8f521bbc220ee3a101e82025d7721e2c108d90922f1a16f9d08a1b18f898ec241a12a12f5e931548e3fb528039d9
  languageName: node
  linkType: hard

"rehype-raw@npm:^7.0.0":
  version: 7.0.0
  resolution: "rehype-raw@npm:7.0.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    hast-util-raw: "npm:^9.0.0"
    vfile: "npm:^6.0.0"
  checksum: 10c0/1435b4b6640a5bc3abe3b2133885c4dbff5ef2190ef9cfe09d6a63f74dd7d7ffd0cede70603278560ccf1acbfb9da9faae4b68065a28bc5aa88ad18e40f32d52
  languageName: node
  linkType: hard

"remark-gfm@npm:^3.0.1":
  version: 3.0.1
  resolution: "remark-gfm@npm:3.0.1"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-gfm: "npm:^2.0.0"
    micromark-extension-gfm: "npm:^2.0.0"
    unified: "npm:^10.0.0"
  checksum: 10c0/53c4e82204f82f81949a170efdeb49d3c45137b7bca06a7ff857a483aac1a44b55ef0de8fb1bbe4f1292f2a378058e2e42e644f2c61f3e0cdc3e56afa4ec2a2c
  languageName: node
  linkType: hard

"remark-math@npm:^5.1.1":
  version: 5.1.1
  resolution: "remark-math@npm:5.1.1"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-math: "npm:^2.0.0"
    micromark-extension-math: "npm:^2.0.0"
    unified: "npm:^10.0.0"
  checksum: 10c0/e61e314398e65d1ef9343cce37bdb8e94697772d53f1b9e48f815cece35033b4d41db81766696135558c6de40f2ad86877b49891daec6c7b1453dba0e034a9dc
  languageName: node
  linkType: hard

"remark-mdx@npm:^2.0.0":
  version: 2.3.0
  resolution: "remark-mdx@npm:2.3.0"
  dependencies:
    mdast-util-mdx: "npm:^2.0.0"
    micromark-extension-mdxjs: "npm:^1.0.0"
  checksum: 10c0/2688bbf03094a9cd17cc86afb6cf0270e86ffc696a2fe25ccb1befb84eb0864d281388dc560b585e05e20f94a994c9fa88492430d2ba703a2fef6918bca4c36b
  languageName: node
  linkType: hard

"remark-parse@npm:^10.0.0":
  version: 10.0.2
  resolution: "remark-parse@npm:10.0.2"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-from-markdown: "npm:^1.0.0"
    unified: "npm:^10.0.0"
  checksum: 10c0/30cb8f2790380b1c7370a1c66cda41f33a7dc196b9e440a00e2675037bca55aea868165a8204e0cdbacc27ef4a3bdb7d45879826bd6efa07d9fdf328cb67a332
  languageName: node
  linkType: hard

"remark-reading-time@npm:^2.0.1":
  version: 2.0.2
  resolution: "remark-reading-time@npm:2.0.2"
  dependencies:
    estree-util-is-identifier-name: "npm:^2.0.0"
    estree-util-value-to-estree: "npm:^3.3.3"
    reading-time: "npm:^1.3.0"
    unist-util-visit: "npm:^3.1.0"
  checksum: 10c0/d0b1c393e70625bba407887b5df8d1b1a8147069188ffe098e19985fd02bd7422e015a3b9e3b1b5d5a1a79de6b3a68d2c90158b7568226773fea979ac96c8c9c
  languageName: node
  linkType: hard

"remark-rehype@npm:^10.0.0":
  version: 10.1.0
  resolution: "remark-rehype@npm:10.1.0"
  dependencies:
    "@types/hast": "npm:^2.0.0"
    "@types/mdast": "npm:^3.0.0"
    mdast-util-to-hast: "npm:^12.1.0"
    unified: "npm:^10.0.0"
  checksum: 10c0/803e658c9b51a9b53ee2ada42ff82e8e570444bb97c873e0d602c2d8dcb69a774fd22bd6f26643dfd5ab4c181059ea6c9fb9a99a2d7f9665f3f11bef1a1489bd
  languageName: node
  linkType: hard

"robust-predicates@npm:^3.0.2":
  version: 3.0.2
  resolution: "robust-predicates@npm:3.0.2"
  checksum: 10c0/4ecd53649f1c2d49529c85518f2fa69ffb2f7a4453f7fd19c042421c7b4d76c3efb48bc1c740c8f7049346d7cb58cf08ee0c9adaae595cc23564d360adb1fde4
  languageName: node
  linkType: hard

"root-workspace-0b6124@workspace:.":
  version: 0.0.0-use.local
  resolution: "root-workspace-0b6124@workspace:."
  dependencies:
    "@types/node": "npm:^18.0.0"
    "@types/react": "npm:^18.0.14"
    "@types/react-dom": "npm:^18.0.5"
    gray-matter: "npm:^4.0.3"
    next: "npm:latest"
    nextra: "npm:^2.0.0-beta.5"
    nextra-theme-blog: "npm:^2.0.0-beta.5"
    react: "npm:^18.2.0"
    react-dom: "npm:^18.2.0"
    rss: "npm:^1.2.2"
    typescript: "npm:^4.7.4"
  languageName: unknown
  linkType: soft

"rss@npm:^1.2.2":
  version: 1.2.2
  resolution: "rss@npm:1.2.2"
  dependencies:
    mime-types: "npm:2.1.13"
    xml: "npm:1.0.1"
  checksum: 10c0/7e37a6ed1820b69476c76e4fefe09f69dd7505408e9fc7690441a5190aca2e2e2faf8d19575fd1efd66debfb178893374fbff9abf68e5416f5e4d4cccb7ae8b9
  languageName: node
  linkType: hard

"rw@npm:1":
  version: 1.3.3
  resolution: "rw@npm:1.3.3"
  checksum: 10c0/b1e1ef37d1e79d9dc7050787866e30b6ddcb2625149276045c262c6b4d53075ddc35f387a856a8e76f0d0df59f4cd58fe24707e40797ebee66e542b840ed6a53
  languageName: node
  linkType: hard

"sade@npm:^1.7.3":
  version: 1.8.1
  resolution: "sade@npm:1.8.1"
  dependencies:
    mri: "npm:^1.1.0"
  checksum: 10c0/da8a3a5d667ad5ce3bf6d4f054bbb9f711103e5df21003c5a5c1a8a77ce12b640ed4017dd423b13c2307ea7e645adee7c2ae3afe8051b9db16a6f6d3da3f90b1
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"scheduler@npm:^0.23.2":
  version: 0.23.2
  resolution: "scheduler@npm:0.23.2"
  dependencies:
    loose-envify: "npm:^1.1.0"
  checksum: 10c0/26383305e249651d4c58e6705d5f8425f153211aef95f15161c151f7b8de885f24751b377e4a0b3dd42cce09aad3f87a61dab7636859c0d89b7daf1a1e2a5c78
  languageName: node
  linkType: hard

"section-matter@npm:^1.0.0":
  version: 1.0.0
  resolution: "section-matter@npm:1.0.0"
  dependencies:
    extend-shallow: "npm:^2.0.1"
    kind-of: "npm:^6.0.0"
  checksum: 10c0/8007f91780adc5aaa781a848eaae50b0f680bbf4043b90cf8a96778195b8fab690c87fe7a989e02394ce69890e330811ec8dab22397d384673ce59f7d750641d
  languageName: node
  linkType: hard

"semver@npm:^7.7.2":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10c0/aca305edfbf2383c22571cb7714f48cadc7ac95371b4b52362fb8eeffdfbc0de0669368b82b2b15978f8848f01d7114da65697e56cd8c37b0dab8c58e543f9ea
  languageName: node
  linkType: hard

"sharp@npm:^0.34.3":
  version: 0.34.3
  resolution: "sharp@npm:0.34.3"
  dependencies:
    "@img/sharp-darwin-arm64": "npm:0.34.3"
    "@img/sharp-darwin-x64": "npm:0.34.3"
    "@img/sharp-libvips-darwin-arm64": "npm:1.2.0"
    "@img/sharp-libvips-darwin-x64": "npm:1.2.0"
    "@img/sharp-libvips-linux-arm": "npm:1.2.0"
    "@img/sharp-libvips-linux-arm64": "npm:1.2.0"
    "@img/sharp-libvips-linux-ppc64": "npm:1.2.0"
    "@img/sharp-libvips-linux-s390x": "npm:1.2.0"
    "@img/sharp-libvips-linux-x64": "npm:1.2.0"
    "@img/sharp-libvips-linuxmusl-arm64": "npm:1.2.0"
    "@img/sharp-libvips-linuxmusl-x64": "npm:1.2.0"
    "@img/sharp-linux-arm": "npm:0.34.3"
    "@img/sharp-linux-arm64": "npm:0.34.3"
    "@img/sharp-linux-ppc64": "npm:0.34.3"
    "@img/sharp-linux-s390x": "npm:0.34.3"
    "@img/sharp-linux-x64": "npm:0.34.3"
    "@img/sharp-linuxmusl-arm64": "npm:0.34.3"
    "@img/sharp-linuxmusl-x64": "npm:0.34.3"
    "@img/sharp-wasm32": "npm:0.34.3"
    "@img/sharp-win32-arm64": "npm:0.34.3"
    "@img/sharp-win32-ia32": "npm:0.34.3"
    "@img/sharp-win32-x64": "npm:0.34.3"
    color: "npm:^4.2.3"
    detect-libc: "npm:^2.0.4"
    semver: "npm:^7.7.2"
  dependenciesMeta:
    "@img/sharp-darwin-arm64":
      optional: true
    "@img/sharp-darwin-x64":
      optional: true
    "@img/sharp-libvips-darwin-arm64":
      optional: true
    "@img/sharp-libvips-darwin-x64":
      optional: true
    "@img/sharp-libvips-linux-arm":
      optional: true
    "@img/sharp-libvips-linux-arm64":
      optional: true
    "@img/sharp-libvips-linux-ppc64":
      optional: true
    "@img/sharp-libvips-linux-s390x":
      optional: true
    "@img/sharp-libvips-linux-x64":
      optional: true
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
    "@img/sharp-linux-arm":
      optional: true
    "@img/sharp-linux-arm64":
      optional: true
    "@img/sharp-linux-ppc64":
      optional: true
    "@img/sharp-linux-s390x":
      optional: true
    "@img/sharp-linux-x64":
      optional: true
    "@img/sharp-linuxmusl-arm64":
      optional: true
    "@img/sharp-linuxmusl-x64":
      optional: true
    "@img/sharp-wasm32":
      optional: true
    "@img/sharp-win32-arm64":
      optional: true
    "@img/sharp-win32-ia32":
      optional: true
    "@img/sharp-win32-x64":
      optional: true
  checksum: 10c0/df9e6645e3db6ed298a0ac956ba74e468c367fc038b547936fbdddc6a29fce9af40413acbef73b3716291530760f311a20e45c8983f20ee5ea69dd2f21464a2b
  languageName: node
  linkType: hard

"shebang-command@npm:^1.2.0":
  version: 1.2.0
  resolution: "shebang-command@npm:1.2.0"
  dependencies:
    shebang-regex: "npm:^1.0.0"
  checksum: 10c0/7b20dbf04112c456b7fc258622dafd566553184ac9b6938dd30b943b065b21dabd3776460df534cc02480db5e1b6aec44700d985153a3da46e7db7f9bd21326d
  languageName: node
  linkType: hard

"shebang-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "shebang-regex@npm:1.0.0"
  checksum: 10c0/9abc45dee35f554ae9453098a13fdc2f1730e525a5eb33c51f096cc31f6f10a4b38074c1ebf354ae7bffa7229506083844008dfc3bb7818228568c0b2dc1fff2
  languageName: node
  linkType: hard

"shiki@npm:^0.14.3":
  version: 0.14.7
  resolution: "shiki@npm:0.14.7"
  dependencies:
    ansi-sequence-parser: "npm:^1.1.0"
    jsonc-parser: "npm:^3.2.0"
    vscode-oniguruma: "npm:^1.7.0"
    vscode-textmate: "npm:^8.0.0"
  checksum: 10c0/5c7fcbb870d0facccc7ae2f3410a28121f8e0b3f298e4e956de817ad6ab60a4c7e20a9184edfe50a93447addbb88b95b69e6ef88ac16ac6ca3e94c50771a6459
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.0":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 10c0/25d272fa73e146048565e08f3309d5b942c1979a6f4a58a8c59d5fa299728e9c2fcd1a759ec870863b1fd38653670240cd420dad2ad9330c71f36608a6a1c912
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: "npm:^0.3.1"
  checksum: 10c0/df5e4662a8c750bdba69af4e8263c5d96fe4cd0f9fe4bdfa3cbdeb45d2e869dff640beaaeb1ef0e99db4d8d2ec92f85508c269f50c972174851bc1ae5bd64308
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 10c0/e18488c6a42bdfd4ac5be85b2ced3ccd0224773baae6ad42cfbb9ec74fc07f9fa8396bd35ee638084ead7a2a0818eb5e7151111544d4731ce843019dab4be47b
  languageName: node
  linkType: hard

"sort-keys@npm:^5.0.0":
  version: 5.1.0
  resolution: "sort-keys@npm:5.1.0"
  dependencies:
    is-plain-obj: "npm:^4.0.0"
  checksum: 10c0/fdb7aeb02368ad91b2ea947b59f3c95d80f8c71bbcb5741ebd55852994f54a129af3b3663b280951566fe5897de056428810dbb58c61db831e588c0ac110f2b0
  languageName: node
  linkType: hard

"source-map-js@npm:^1.0.2":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10c0/7bda1fc4c197e3c6ff17de1b8b2c20e60af81b63a52cb32ec5a5d67a20a7d42651e2cb34ebe93833c5a2a084377e17455854fee3e21e7925c64a51b6a52b0faf
  languageName: node
  linkType: hard

"source-map@npm:^0.7.0":
  version: 0.7.4
  resolution: "source-map@npm:0.7.4"
  checksum: 10c0/dc0cf3768fe23c345ea8760487f8c97ef6fca8a73c83cd7c9bf2fde8bc2c34adb9c0824d6feb14bc4f9e37fb522e18af621543f1289038a66ac7586da29aa7dc
  languageName: node
  linkType: hard

"space-separated-tokens@npm:^2.0.0":
  version: 2.0.2
  resolution: "space-separated-tokens@npm:2.0.2"
  checksum: 10c0/6173e1d903dca41dcab6a2deed8b4caf61bd13b6d7af8374713500570aa929ff9414ae09a0519f4f8772df993300305a395d4871f35bc4ca72b6db57e1f30af8
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 10c0/ecadcfe4c771890140da5023d43e190b7566d9cf8b2d238600f31bec0fc653f328da4450eb04bd59a431771a8e9cc0e118f0aa3974b683a4981b4e07abc2a5bb
  languageName: node
  linkType: hard

"stringify-entities@npm:^4.0.0":
  version: 4.0.4
  resolution: "stringify-entities@npm:4.0.4"
  dependencies:
    character-entities-html4: "npm:^2.0.0"
    character-entities-legacy: "npm:^3.0.0"
  checksum: 10c0/537c7e656354192406bdd08157d759cd615724e9d0873602d2c9b2f6a5c0a8d0b1d73a0a08677848105c5eebac6db037b57c0b3a4ec86331117fa7319ed50448
  languageName: node
  linkType: hard

"strip-bom-string@npm:^1.0.0":
  version: 1.0.0
  resolution: "strip-bom-string@npm:1.0.0"
  checksum: 10c0/5c5717e2643225aa6a6d659d34176ab2657037f1fe2423ac6fcdb488f135e14fef1022030e426d8b4d0989e09adbd5c3288d5d3b9c632abeefd2358dfc512bca
  languageName: node
  linkType: hard

"strip-eof@npm:^1.0.0":
  version: 1.0.0
  resolution: "strip-eof@npm:1.0.0"
  checksum: 10c0/f336beed8622f7c1dd02f2cbd8422da9208fae81daf184f73656332899978919d5c0ca84dc6cfc49ad1fc4dd7badcde5412a063cf4e0d7f8ed95a13a63f68f45
  languageName: node
  linkType: hard

"style-to-object@npm:^0.4.1":
  version: 0.4.4
  resolution: "style-to-object@npm:0.4.4"
  dependencies:
    inline-style-parser: "npm:0.1.1"
  checksum: 10c0/3a733080da66952881175b17d65f92985cf94c1ca358a92cf21b114b1260d49b94a404ed79476047fb95698d64c7e366ca7443f0225939e2fb34c38bbc9c7639
  languageName: node
  linkType: hard

"styled-jsx@npm:5.1.6":
  version: 5.1.6
  resolution: "styled-jsx@npm:5.1.6"
  dependencies:
    client-only: "npm:0.0.1"
  peerDependencies:
    react: ">= 16.8.0 || 17.x.x || ^18.0.0-0 || ^19.0.0-0"
  peerDependenciesMeta:
    "@babel/core":
      optional: true
    babel-plugin-macros:
      optional: true
  checksum: 10c0/ace50e7ea5ae5ae6a3b65a50994c51fca6ae7df9c7ecfd0104c36be0b4b3a9c5c1a2374d16e2a11e256d0b20be6d47256d768ecb4f91ab390f60752a075780f5
  languageName: node
  linkType: hard

"stylis@npm:^4.1.3":
  version: 4.3.6
  resolution: "stylis@npm:4.3.6"
  checksum: 10c0/e736d484983a34f7c65d362c67dc79b7bce388054b261c2b7b23d02eaaf280617033f65d44b1ea341854f4331a5074b885668ac8741f98c13a6cfd6443ae85d0
  languageName: node
  linkType: hard

"supports-color@npm:^4.0.0":
  version: 4.5.0
  resolution: "supports-color@npm:4.5.0"
  dependencies:
    has-flag: "npm:^2.0.0"
  checksum: 10c0/2dc369eeac73954e87037dea1ebae0238b2abc0a39d7e35aa60eb8a84cc8d1dcade8b62e010597f5859f94c937e992abe6a6195460855fcc5e51f8cfc7fcc72a
  languageName: node
  linkType: hard

"title@npm:^3.5.3":
  version: 3.5.3
  resolution: "title@npm:3.5.3"
  dependencies:
    arg: "npm:1.0.0"
    chalk: "npm:2.3.0"
    clipboardy: "npm:1.2.2"
    titleize: "npm:1.0.0"
  bin:
    title: bin/title.js
  checksum: 10c0/9334ff46f49c215a108adbb3ab39bd946dfd1a669b999ad173ff61aa7598a17718f954462d8ebf8fb3ea643b5c37f2f7a163310d186acb18a101c028248d3b15
  languageName: node
  linkType: hard

"titleize@npm:1.0.0":
  version: 1.0.0
  resolution: "titleize@npm:1.0.0"
  checksum: 10c0/7c542bdc5754406839fc61e1a43803cb460cb0b5472f7cecf267bd9498e72d549d7f5cdfadd72ec20c3bb0783d52f4c72fe68e104cecd84195b29a5ffe836510
  languageName: node
  linkType: hard

"trim-lines@npm:^3.0.0":
  version: 3.0.1
  resolution: "trim-lines@npm:3.0.1"
  checksum: 10c0/3a1611fa9e52aa56a94c69951a9ea15b8aaad760eaa26c56a65330dc8adf99cb282fc07cc9d94968b7d4d88003beba220a7278bbe2063328eb23fb56f9509e94
  languageName: node
  linkType: hard

"trough@npm:^2.0.0":
  version: 2.2.0
  resolution: "trough@npm:2.2.0"
  checksum: 10c0/58b671fc970e7867a48514168894396dd94e6d9d6456aca427cc299c004fe67f35ed7172a36449086b2edde10e78a71a284ec0076809add6834fb8f857ccb9b0
  languageName: node
  linkType: hard

"ts-dedent@npm:^2.2.0":
  version: 2.2.0
  resolution: "ts-dedent@npm:2.2.0"
  checksum: 10c0/175adea838468cc2ff7d5e97f970dcb798bbcb623f29c6088cb21aa2880d207c5784be81ab1741f56b9ac37840cbaba0c0d79f7f8b67ffe61c02634cafa5c303
  languageName: node
  linkType: hard

"tslib@npm:^2.4.0, tslib@npm:^2.8.0":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10c0/9c4759110a19c53f992d9aae23aac5ced636e99887b51b9e61def52611732872ff7668757d4e4c61f19691e36f4da981cd9485e869b4a7408d689f6bf1f14e62
  languageName: node
  linkType: hard

"type-fest@npm:^1.0.2":
  version: 1.4.0
  resolution: "type-fest@npm:1.4.0"
  checksum: 10c0/a3c0f4ee28ff6ddf800d769eafafcdeab32efa38763c1a1b8daeae681920f6e345d7920bf277245235561d8117dab765cb5f829c76b713b4c9de0998a5397141
  languageName: node
  linkType: hard

"typescript@npm:^4.7.4":
  version: 4.9.5
  resolution: "typescript@npm:4.9.5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/5f6cad2e728a8a063521328e612d7876e12f0d8a8390d3b3aaa452a6a65e24e9ac8ea22beb72a924fd96ea0a49ea63bb4e251fb922b12eedfb7f7a26475e5c56
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A^4.7.4#optional!builtin<compat/typescript>":
  version: 4.9.5
  resolution: "typescript@patch:typescript@npm%3A4.9.5#optional!builtin<compat/typescript>::version=4.9.5&hash=289587"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/e3333f887c6829dfe0ab6c1dbe0dd1e3e2aeb56c66460cb85c5440c566f900c833d370ca34eb47558c0c69e78ced4bfe09b8f4f98b6de7afed9b84b8d1dd06a1
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 10c0/bb673d7876c2d411b6eb6c560e0c571eef4a01c1c19925175d16e3a30c4c428181fb8d7ae802a261f283e4166a0ac435e2f505743aa9e45d893f9a3df017b501
  languageName: node
  linkType: hard

"unified@npm:^10.0.0":
  version: 10.1.2
  resolution: "unified@npm:10.1.2"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    bail: "npm:^2.0.0"
    extend: "npm:^3.0.0"
    is-buffer: "npm:^2.0.0"
    is-plain-obj: "npm:^4.0.0"
    trough: "npm:^2.0.0"
    vfile: "npm:^5.0.0"
  checksum: 10c0/da9195e3375a74ab861a65e1d7b0454225d17a61646697911eb6b3e97de41091930ed3d167eb11881d4097c51deac407091d39ddd1ee8bf1fde3f946844a17a7
  languageName: node
  linkType: hard

"unist-util-find-after@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-find-after@npm:5.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-is: "npm:^6.0.0"
  checksum: 10c0/a7cea473c4384df8de867c456b797ff1221b20f822e1af673ff5812ed505358b36f47f3b084ac14c3622cb879ed833b71b288e8aa71025352a2aab4c2925a6eb
  languageName: node
  linkType: hard

"unist-util-generated@npm:^2.0.0":
  version: 2.0.1
  resolution: "unist-util-generated@npm:2.0.1"
  checksum: 10c0/6f052dd47a7280785f3787f52cdfe8819e1de50317a1bcf7c9346c63268cf2cebc61a5980e7ca734a54735e27dbb73091aa0361a98504ab7f9409fb75f1b16bb
  languageName: node
  linkType: hard

"unist-util-is@npm:^5.0.0":
  version: 5.2.1
  resolution: "unist-util-is@npm:5.2.1"
  dependencies:
    "@types/unist": "npm:^2.0.0"
  checksum: 10c0/a2376910b832bb10653d2167c3cd85b3610a5fd53f5169834c08b3c3a720fae9043d75ad32d727eedfc611491966c26a9501d428ec62467edc17f270feb5410b
  languageName: node
  linkType: hard

"unist-util-is@npm:^6.0.0":
  version: 6.0.0
  resolution: "unist-util-is@npm:6.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
  checksum: 10c0/9419352181eaa1da35eca9490634a6df70d2217815bb5938a04af3a662c12c5607a2f1014197ec9c426fbef18834f6371bfdb6f033040fa8aa3e965300d70e7e
  languageName: node
  linkType: hard

"unist-util-position-from-estree@npm:^1.0.0, unist-util-position-from-estree@npm:^1.1.0":
  version: 1.1.2
  resolution: "unist-util-position-from-estree@npm:1.1.2"
  dependencies:
    "@types/unist": "npm:^2.0.0"
  checksum: 10c0/1d95d0b2b05efcec07a4e6745a6950cd498f6100fb900615b252937baed5140df1c6319b9a67364c8a6bd891c58b3c9a52a22e8e1d3422c50bb785d7e3ad7484
  languageName: node
  linkType: hard

"unist-util-position@npm:^4.0.0":
  version: 4.0.4
  resolution: "unist-util-position@npm:4.0.4"
  dependencies:
    "@types/unist": "npm:^2.0.0"
  checksum: 10c0/e506d702e25a0fb47a64502054f709a6ff5db98993bf139eec868cd11eb7de34392b781c6c2002e2c24d97aa398c14b32a47076129f36e4b894a2c1351200888
  languageName: node
  linkType: hard

"unist-util-position@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-position@npm:5.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
  checksum: 10c0/dde3b31e314c98f12b4dc6402f9722b2bf35e96a4f2d463233dd90d7cde2d4928074a7a11eff0a5eb1f4e200f27fc1557e0a64a7e8e4da6558542f251b1b7400
  languageName: node
  linkType: hard

"unist-util-remove-position@npm:^4.0.0":
  version: 4.0.2
  resolution: "unist-util-remove-position@npm:4.0.2"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    unist-util-visit: "npm:^4.0.0"
  checksum: 10c0/17371b1e53c52d1b00656c9c6fe1bb044846e7067022195823ed3d1a8d8b965d4f9a79b286b8a841e68731b4ec93afd563b81ae92151f80c28534ba51e9dc18f
  languageName: node
  linkType: hard

"unist-util-remove-position@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-remove-position@npm:5.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-visit: "npm:^5.0.0"
  checksum: 10c0/e8c76da4399446b3da2d1c84a97c607b37d03d1d92561e14838cbe4fdcb485bfc06c06cfadbb808ccb72105a80643976d0660d1fe222ca372203075be9d71105
  languageName: node
  linkType: hard

"unist-util-remove@npm:^4.0.0":
  version: 4.0.0
  resolution: "unist-util-remove@npm:4.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-is: "npm:^6.0.0"
    unist-util-visit-parents: "npm:^6.0.0"
  checksum: 10c0/30f3ed31095dd7f3109266d39c514fab5f2da3fb656d5f78a0e3e7700f219760f2f4d8286c810ae43c241fee3f0a8dd40f8d1e5ebeee3cb810581d5e7e8d4f7d
  languageName: node
  linkType: hard

"unist-util-stringify-position@npm:^3.0.0":
  version: 3.0.3
  resolution: "unist-util-stringify-position@npm:3.0.3"
  dependencies:
    "@types/unist": "npm:^2.0.0"
  checksum: 10c0/14550027825230528f6437dad7f2579a841780318569851291be6c8a970bae6f65a7feb24dabbcfce0e5e68cacae85bf12cbda3f360f7c873b4db602bdf7bb21
  languageName: node
  linkType: hard

"unist-util-stringify-position@npm:^4.0.0":
  version: 4.0.0
  resolution: "unist-util-stringify-position@npm:4.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
  checksum: 10c0/dfe1dbe79ba31f589108cb35e523f14029b6675d741a79dea7e5f3d098785045d556d5650ec6a8338af11e9e78d2a30df12b1ee86529cded1098da3f17ee999e
  languageName: node
  linkType: hard

"unist-util-visit-parents@npm:^4.0.0":
  version: 4.1.1
  resolution: "unist-util-visit-parents@npm:4.1.1"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    unist-util-is: "npm:^5.0.0"
  checksum: 10c0/f84b544a111af5a17f2b80c462da9f7fdcb46a69f85ab317d2d9ddca766c00e2ceea6c76c0960e58ef4607aad89661c99eccf290973b453e15dd1621c57079d4
  languageName: node
  linkType: hard

"unist-util-visit-parents@npm:^5.0.0, unist-util-visit-parents@npm:^5.1.1":
  version: 5.1.3
  resolution: "unist-util-visit-parents@npm:5.1.3"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    unist-util-is: "npm:^5.0.0"
  checksum: 10c0/f6829bfd8f2eddf63a32e2c302cd50978ef0c194b792c6fe60c2b71dfd7232415a3c5941903972543e9d34e6a8ea69dee9ccd95811f4a795495ed2ae855d28d0
  languageName: node
  linkType: hard

"unist-util-visit-parents@npm:^6.0.0":
  version: 6.0.1
  resolution: "unist-util-visit-parents@npm:6.0.1"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-is: "npm:^6.0.0"
  checksum: 10c0/51b1a5b0aa23c97d3e03e7288f0cdf136974df2217d0999d3de573c05001ef04cccd246f51d2ebdfb9e8b0ed2704451ad90ba85ae3f3177cf9772cef67f56206
  languageName: node
  linkType: hard

"unist-util-visit@npm:^3.1.0":
  version: 3.1.0
  resolution: "unist-util-visit@npm:3.1.0"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    unist-util-is: "npm:^5.0.0"
    unist-util-visit-parents: "npm:^4.0.0"
  checksum: 10c0/9b92ea4e6debadbb77f2c7a0ab8c8b7c63781b2f2050563c971687df368f6f6fe932d864442347a685f0dc56b570a55e5d7ffeb87a452489100640cf280dc8da
  languageName: node
  linkType: hard

"unist-util-visit@npm:^4.0.0":
  version: 4.1.2
  resolution: "unist-util-visit@npm:4.1.2"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    unist-util-is: "npm:^5.0.0"
    unist-util-visit-parents: "npm:^5.1.1"
  checksum: 10c0/56a1f49a4d8e321e75b3c7821d540a45165a031dd06324bb0e8c75e7737bc8d73bdddbf0b0ca82000f9708a4c36861c6ebe88d01f7cf00e925f5d75f13a3a017
  languageName: node
  linkType: hard

"unist-util-visit@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-visit@npm:5.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-is: "npm:^6.0.0"
    unist-util-visit-parents: "npm:^6.0.0"
  checksum: 10c0/51434a1d80252c1540cce6271a90fd1a106dbe624997c09ed8879279667fb0b2d3a685e02e92bf66598dcbe6cdffa7a5f5fb363af8fdf90dda6c855449ae39a5
  languageName: node
  linkType: hard

"uuid@npm:^9.0.0":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/1607dd32ac7fc22f2d8f77051e6a64845c9bce5cd3dd8aa0070c074ec73e666a1f63c7b4e0f4bf2bc8b9d59dc85a15e17807446d9d2b17c8485fbc2147b27f9b
  languageName: node
  linkType: hard

"uvu@npm:^0.5.0":
  version: 0.5.6
  resolution: "uvu@npm:0.5.6"
  dependencies:
    dequal: "npm:^2.0.0"
    diff: "npm:^5.0.0"
    kleur: "npm:^4.0.3"
    sade: "npm:^1.7.3"
  bin:
    uvu: bin.js
  checksum: 10c0/ad32eb5f7d94bdeb71f80d073003f0138e24f61ed68cecc8e15d2f30838f44c9670577bb1775c8fac894bf93d1bc1583d470a9195e49bfa6efa14cc6f4942bff
  languageName: node
  linkType: hard

"vfile-location@npm:^5.0.0":
  version: 5.0.3
  resolution: "vfile-location@npm:5.0.3"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    vfile: "npm:^6.0.0"
  checksum: 10c0/1711f67802a5bc175ea69750d59863343ed43d1b1bb25c0a9063e4c70595e673e53e2ed5cdbb6dcdc370059b31605144d95e8c061b9361bcc2b036b8f63a4966
  languageName: node
  linkType: hard

"vfile-matter@npm:^3.0.1":
  version: 3.0.1
  resolution: "vfile-matter@npm:3.0.1"
  dependencies:
    "@types/js-yaml": "npm:^4.0.0"
    is-buffer: "npm:^2.0.0"
    js-yaml: "npm:^4.0.0"
  checksum: 10c0/45ff9b49e7a5817b646d76f14d2486e12a93a16951bd8cfa6c64f0c78c4e56e48d30a0542a980bc9c7aae1bb430d457f9dfc2677e514d66cc2976ab31f10403a
  languageName: node
  linkType: hard

"vfile-message@npm:^3.0.0":
  version: 3.1.4
  resolution: "vfile-message@npm:3.1.4"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    unist-util-stringify-position: "npm:^3.0.0"
  checksum: 10c0/c4ccf9c0ced92d657846fd067fefcf91c5832cdbe2ecc431bb67886e8c959bf7fc05a9dbbca5551bc34c9c87a0a73854b4249f65c64ddfebc4d59ea24a18b996
  languageName: node
  linkType: hard

"vfile-message@npm:^4.0.0":
  version: 4.0.2
  resolution: "vfile-message@npm:4.0.2"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-stringify-position: "npm:^4.0.0"
  checksum: 10c0/07671d239a075f888b78f318bc1d54de02799db4e9dce322474e67c35d75ac4a5ac0aaf37b18801d91c9f8152974ea39678aa72d7198758b07f3ba04fb7d7514
  languageName: node
  linkType: hard

"vfile@npm:^5.0.0, vfile@npm:^5.3.0":
  version: 5.3.7
  resolution: "vfile@npm:5.3.7"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    is-buffer: "npm:^2.0.0"
    unist-util-stringify-position: "npm:^3.0.0"
    vfile-message: "npm:^3.0.0"
  checksum: 10c0/c36bd4c3f16ec0c6cbad0711ca99200316bbf849d6b07aa4cb5d9062cc18ae89249fe62af9521926e9659c0e6bc5c2c1da0fe26b41fb71e757438297e1a41da4
  languageName: node
  linkType: hard

"vfile@npm:^6.0.0":
  version: 6.0.3
  resolution: "vfile@npm:6.0.3"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    vfile-message: "npm:^4.0.0"
  checksum: 10c0/e5d9eb4810623f23758cfc2205323e33552fb5972e5c2e6587babe08fe4d24859866277404fb9e2a20afb71013860d96ec806cb257536ae463c87d70022ab9ef
  languageName: node
  linkType: hard

"vscode-oniguruma@npm:^1.7.0":
  version: 1.7.0
  resolution: "vscode-oniguruma@npm:1.7.0"
  checksum: 10c0/bef0073c665ddf8c86e51da94529c905856559e9aba97a9882f951acd572da560384775941ab6e7e8db94d9c578b25fefb951e4b73c37e8712e16b0231de2689
  languageName: node
  linkType: hard

"vscode-textmate@npm:^8.0.0":
  version: 8.0.0
  resolution: "vscode-textmate@npm:8.0.0"
  checksum: 10c0/836f7fe73fc94998a38ca193df48173a2b6eab08b4943d83c8cac9a2a0c3546cfdab4cf1b10b890ec4a4374c5bee03a885ef0e83e7fd2bd618cf00781c017c04
  languageName: node
  linkType: hard

"web-namespaces@npm:^2.0.0":
  version: 2.0.1
  resolution: "web-namespaces@npm:2.0.1"
  checksum: 10c0/df245f466ad83bd5cd80bfffc1674c7f64b7b84d1de0e4d2c0934fb0782e0a599164e7197a4bce310ee3342fd61817b8047ff04f076a1ce12dd470584142a4bd
  languageName: node
  linkType: hard

"web-worker@npm:^1.2.0":
  version: 1.5.0
  resolution: "web-worker@npm:1.5.0"
  checksum: 10c0/d42744757422803c73ca64fa51e1ce994354ace4b8438b3f740425a05afeb8df12dd5dadbf6b0839a08dbda56c470d7943c0383854c4fb1ae40ab874eb10427a
  languageName: node
  linkType: hard

"which@npm:^1.2.9":
  version: 1.3.1
  resolution: "which@npm:1.3.1"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    which: ./bin/which
  checksum: 10c0/e945a8b6bbf6821aaaef7f6e0c309d4b615ef35699576d5489b4261da9539f70393c6b2ce700ee4321c18f914ebe5644bc4631b15466ffbaad37d83151f6af59
  languageName: node
  linkType: hard

"xml@npm:1.0.1":
  version: 1.0.1
  resolution: "xml@npm:1.0.1"
  checksum: 10c0/04bcc9b8b5e7b49392072fbd9c6b0f0958bd8e8f8606fee460318e43991349a68cbc5384038d179ff15aef7d222285f69ca0f067f53d071084eb14c7fdb30411
  languageName: node
  linkType: hard

"yallist@npm:^2.1.2":
  version: 2.1.2
  resolution: "yallist@npm:2.1.2"
  checksum: 10c0/0b9e25aa00adf19e01d2bcd4b208aee2b0db643d9927131797b7af5ff69480fc80f1c3db738cbf3946f0bddf39d8f2d0a5709c644fd42d4aa3a4e6e786c087b5
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10c0/dceb44c28578b31641e13695d200d34ec4ab3966a5729814d5445b194933c096b7ced71494ce53a0e8820685d1d010df8b2422e5bf2cdea7e469d97ffbea306f
  languageName: node
  linkType: hard

"zod@npm:^3.22.3":
  version: 3.25.76
  resolution: "zod@npm:3.25.76"
  checksum: 10c0/5718ec35e3c40b600316c5b4c5e4976f7fee68151bc8f8d90ec18a469be9571f072e1bbaace10f1e85cf8892ea12d90821b200e980ab46916a6166a4260a983c
  languageName: node
  linkType: hard

"zwitch@npm:^2.0.0":
  version: 2.0.4
  resolution: "zwitch@npm:2.0.4"
  checksum: 10c0/3c7830cdd3378667e058ffdb4cf2bb78ac5711214e2725900873accb23f3dfe5f9e7e5a06dcdc5f29605da976fc45c26d9a13ca334d6eea2245a15e77b8fc06e
  languageName: node
  linkType: hard
